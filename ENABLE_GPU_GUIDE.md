# 🎮 Enable GPU Acceleration Guide

## 🔍 **Your Hardware Detected**

✅ **Intel Arc Graphics** (~2GB VRAM)
- Excellent for AI workloads!
- Supports PyTorch acceleration
- Much faster than CPU for YOLO inference

---

## 🚀 **Quick Setup (Recommended)**

### **Option 1: Intel XPU (Best for Intel Arc)**

```bash
# Uninstall current PyTorch
pip uninstall torch torchvision torchaudio

# Install Intel Extension for PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install intel-extension-for-pytorch
```

### **Option 2: DirectML (Windows Alternative)**

```bash
# Uninstall current PyTorch
pip uninstall torch torchvision torchaudio

# Install DirectML version
pip install torch-directml
pip install torchvision torchaudio
```

---

## 🛠️ **Step-by-Step Instructions**

### **Step 1: Check Current Installation**
```bash
python test_gpu.py
```
*This shows your current PyTorch version (CPU-only)*

### **Step 2: Choose Your Method**

#### **Method A: Intel XPU (Recommended)**
```bash
# Stop the app first (Ctrl+C in terminal)
pip uninstall torch torchvision torchaudio -y
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install intel-extension-for-pytorch
```

#### **Method B: DirectML (Alternative)**
```bash
# Stop the app first (Ctrl+C in terminal)  
pip uninstall torch torchvision torchaudio -y
pip install torch-directml torchvision torchaudio
```

### **Step 3: Verify Installation**
```bash
python test_gpu.py
```
*Should now show GPU available*

### **Step 4: Restart Your App**
```bash
python app.py
```
*GPU status should now show as enabled*

---

## 🧪 **Test Scripts**

### **Quick GPU Test**
```bash
python -c "
import torch
print('PyTorch version:', torch.__version__)
print('GPU available:', torch.cuda.is_available() or hasattr(torch, 'xpu'))
if hasattr(torch, 'xpu') and torch.xpu.is_available():
    print('Intel XPU available:', torch.xpu.is_available())
    print('XPU device count:', torch.xpu.device_count())
"
```

### **YOLO GPU Test**
```bash
python -c "
from ultralytics import YOLO
model = YOLO('yolo11n.pt')
print('Model device:', model.device)
"
```

---

## 🎯 **Expected Results**

### **Before (CPU Only)**
- CUDA Available: ❌ No
- GPU Name: No GPU detected
- Processing Device: 🖥️ CPU Only
- Inference Speed: ~50-100ms per frame

### **After (GPU Enabled)**
- CUDA Available: ✅ Yes (or XPU Available)
- GPU Name: Intel Arc Graphics
- Processing Device: 🎮 GPU (Intel Arc)
- Inference Speed: ~10-30ms per frame (3-5x faster!)

---

## 🛠️ **Troubleshooting**

### **If Intel XPU doesn't work:**
1. **Update Intel Arc drivers** from Intel's website
2. **Try DirectML method** instead
3. **Check Windows version** (Windows 11 recommended)

### **If DirectML doesn't work:**
1. **Update Windows** to latest version
2. **Update GPU drivers**
3. **Try Intel XPU method** instead

### **If nothing works:**
- Your app will still work on CPU (just slower)
- Consider upgrading to dedicated NVIDIA GPU for best performance

---

## 📊 **Performance Comparison**

| Device | Speed | Power | Compatibility |
|--------|-------|-------|---------------|
| CPU Only | 1x | Low | 100% |
| Intel Arc | 3-5x | Medium | 95% |
| NVIDIA GPU | 5-10x | High | 100% |

---

## 🎉 **Ready to Enable GPU?**

1. **Stop your app** (Ctrl+C in terminal)
2. **Choose a method** (Intel XPU recommended)
3. **Run the installation commands**
4. **Test with** `python test_gpu.py`
5. **Restart app** with `python app.py`
6. **Check web interface** - GPU status should be green!

**Your Intel Arc GPU will make YOLO detection much faster! 🚀**
