#!/usr/bin/env python3
"""
GPU Detection Test Script
Tests GPU detection and provides installation guidance
"""

import sys

def test_torch_gpu():
    """Test PyTorch GPU availability (CUDA, XPU, DirectML)"""
    try:
        import torch
        print("🔍 PyTorch GPU Test:")
        print(f"   PyTorch version: {torch.__version__}")

        gpu_available = False

        # Test CUDA (NVIDIA)
        if torch.cuda.is_available():
            print(f"   ✅ CUDA available: {torch.cuda.device_count()} devices")
            for i in range(torch.cuda.device_count()):
                print(f"      Device {i}: {torch.cuda.get_device_name(i)}")
                memory_info = torch.cuda.mem_get_info(i)
                total_gb = memory_info[1] // (1024**3)
                free_gb = memory_info[0] // (1024**3)
                print(f"         Memory: {free_gb}/{total_gb} GB free")
            gpu_available = True

        # Test Intel XPU (Intel Arc)
        elif hasattr(torch, 'xpu'):
            if torch.xpu.is_available():
                print(f"   ✅ Intel XPU available: {torch.xpu.device_count()} devices")
                for i in range(torch.xpu.device_count()):
                    print(f"      Device {i}: Intel XPU {i}")
                gpu_available = True
            else:
                print("   ⚠️  Intel XPU not available")

        # Test DirectML
        elif hasattr(torch, 'dml'):
            print("   ✅ DirectML available")
            gpu_available = True

        if not gpu_available:
            print("   ⚠️  No GPU acceleration detected")
            print("   💡 You have Intel Arc Graphics! To enable GPU:")
            print("      Run: python enable_gpu.py")
            print("      Or see: ENABLE_GPU_GUIDE.md")

        return gpu_available

    except ImportError:
        print("❌ PyTorch not installed")
        return False

def test_gputil():
    """Test GPUtil for GPU monitoring"""
    try:
        import GPUtil
        print("\n🖥️  GPUtil Test:")
        gpus = GPUtil.getGPUs()
        
        if gpus:
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu.name}")
                print(f"      Load: {gpu.load*100:.1f}%")
                print(f"      Memory: {gpu.memoryUsed}/{gpu.memoryTotal} MB")
                print(f"      Temperature: {gpu.temperature}°C")
        else:
            print("   ⚠️  No GPUs detected by GPUtil")
            
        return len(gpus) > 0
        
    except ImportError:
        print("❌ GPUtil not installed")
        print("   Install with: pip install GPUtil")
        return False

def test_system_info():
    """Test system information"""
    try:
        import psutil
        print("\n💻 System Information:")
        print(f"   CPU cores: {psutil.cpu_count()}")
        print(f"   CPU usage: {psutil.cpu_percent(interval=1):.1f}%")
        
        memory = psutil.virtual_memory()
        print(f"   Memory: {memory.used//1024**3}/{memory.total//1024**3} GB ({memory.percent:.1f}%)")
        
        return True
        
    except ImportError:
        print("❌ psutil not installed")
        return False

def main():
    """Main test function"""
    print("🚀 GPU Detection and System Test")
    print("=" * 50)
    
    # Test PyTorch GPU
    gpu_available = test_torch_gpu()
    
    # Test GPUtil
    gpu_detected = test_gputil()
    
    # Test system info
    system_ok = test_system_info()
    
    print("\n" + "=" * 50)
    print("📊 Summary:")
    
    if gpu_available:
        print("✅ GPU acceleration available - your app will use GPU!")
    else:
        print("⚠️  GPU acceleration not available - app will use CPU")
        print("   You have Intel Arc Graphics - run 'python enable_gpu.py' to enable GPU!")
    
    if gpu_detected:
        print("✅ GPU monitoring available")
    else:
        print("⚠️  GPU monitoring limited")
    
    if system_ok:
        print("✅ System monitoring available")
    
    print("\n🎯 Your YOLO app status:")
    if gpu_available:
        print("   🎮 Will use GPU for inference (fast)")
        print("   📊 Will show GPU utilization in web interface")
    else:
        print("   🖥️  Will use CPU for inference (slower but works)")
        print("   📊 Will show CPU utilization in web interface")
        print("   🚀 Run 'python enable_gpu.py' to enable your Intel Arc GPU!")

if __name__ == "__main__":
    main()
