#!/usr/bin/env python3
"""
GPU Enablement Script for Intel Arc Graphics
Automatically installs the correct PyTorch version for GPU acceleration
"""

import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout.strip():
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def check_current_pytorch():
    """Check current PyTorch installation"""
    try:
        import torch
        print(f"📋 Current PyTorch: {torch.__version__}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
        
        # Check for Intel XPU
        has_xpu = hasattr(torch, 'xpu')
        if has_xpu:
            print(f"   Intel XPU available: {torch.xpu.is_available()}")
        
        return torch.__version__, torch.cuda.is_available(), has_xpu
    except ImportError:
        print("❌ PyTorch not installed")
        return None, False, False

def detect_gpu():
    """Detect available GPU"""
    print("🔍 Detecting GPU hardware...")
    
    # Check for Intel Arc
    try:
        result = subprocess.run(
            'Get-WmiObject Win32_VideoController | Select-Object Name',
            shell=True, capture_output=True, text=True
        )
        if "Intel(R) Arc" in result.stdout:
            print("✅ Intel Arc Graphics detected")
            return "intel_arc"
    except:
        pass
    
    # Check for NVIDIA
    try:
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ NVIDIA GPU detected")
            return "nvidia"
    except:
        pass
    
    print("⚠️  No supported GPU detected")
    return "none"

def install_intel_xpu():
    """Install Intel Extension for PyTorch"""
    print("\n🎮 Installing Intel XPU support...")
    
    # Uninstall current PyTorch
    if not run_command(
        f"{sys.executable} -m pip uninstall torch torchvision torchaudio -y",
        "Uninstalling current PyTorch"
    ):
        return False
    
    # Install CPU PyTorch first
    if not run_command(
        f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu",
        "Installing PyTorch CPU version"
    ):
        return False
    
    # Install Intel Extension
    if not run_command(
        f"{sys.executable} -m pip install intel-extension-for-pytorch",
        "Installing Intel Extension for PyTorch"
    ):
        return False
    
    return True

def install_directml():
    """Install DirectML PyTorch"""
    print("\n🎮 Installing DirectML support...")
    
    # Uninstall current PyTorch
    if not run_command(
        f"{sys.executable} -m pip uninstall torch torchvision torchaudio -y",
        "Uninstalling current PyTorch"
    ):
        return False
    
    # Install DirectML PyTorch
    if not run_command(
        f"{sys.executable} -m pip install torch-directml torchvision torchaudio",
        "Installing DirectML PyTorch"
    ):
        return False
    
    return True

def test_gpu_acceleration():
    """Test if GPU acceleration is working"""
    print("\n🧪 Testing GPU acceleration...")
    
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        
        # Test CUDA
        if torch.cuda.is_available():
            print(f"✅ CUDA available: {torch.cuda.device_count()} devices")
            return True
        
        # Test Intel XPU
        if hasattr(torch, 'xpu') and torch.xpu.is_available():
            print(f"✅ Intel XPU available: {torch.xpu.device_count()} devices")
            return True
        
        # Test DirectML
        if hasattr(torch, 'dml'):
            print("✅ DirectML available")
            return True
        
        print("⚠️  No GPU acceleration detected")
        return False
        
    except Exception as e:
        print(f"❌ Error testing GPU: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 GPU Acceleration Setup for Intel Arc Graphics")
    print("=" * 60)
    
    # Check current state
    version, cuda_available, has_xpu = check_current_pytorch()
    
    if cuda_available or has_xpu:
        print("✅ GPU acceleration already enabled!")
        print("   Your app should already be using GPU.")
        return
    
    # Detect GPU
    gpu_type = detect_gpu()
    
    if gpu_type == "none":
        print("\n⚠️  No supported GPU detected.")
        print("   Your app will continue to work on CPU.")
        return
    
    print(f"\n🎯 Recommended setup for your {gpu_type} GPU:")
    
    if gpu_type == "intel_arc":
        print("1. Intel XPU (Recommended)")
        print("2. DirectML (Alternative)")
        
        choice = input("\nChoose method (1 or 2): ").strip()
        
        if choice == "1":
            success = install_intel_xpu()
        elif choice == "2":
            success = install_directml()
        else:
            print("❌ Invalid choice")
            return
    
    elif gpu_type == "nvidia":
        print("Installing CUDA PyTorch...")
        success = run_command(
            f"{sys.executable} -m pip uninstall torch torchvision torchaudio -y && "
            f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121",
            "Installing CUDA PyTorch"
        )
    
    if success:
        print("\n🎉 Installation completed!")
        
        # Test the installation
        if test_gpu_acceleration():
            print("\n✅ GPU acceleration is now enabled!")
            print("\n🚀 Next steps:")
            print("1. Restart your YOLO app: python app.py")
            print("2. Check the web interface - GPU status should be green")
            print("3. Enjoy faster inference speeds!")
        else:
            print("\n⚠️  GPU acceleration may not be working properly.")
            print("   Check the troubleshooting guide in ENABLE_GPU_GUIDE.md")
    else:
        print("\n❌ Installation failed.")
        print("   Check the error messages above and try manual installation.")

if __name__ == "__main__":
    main()
