# 🚀 Quick Start Guide

## 🎯 How to Run the YOLO Detection App

### ✅ **Simple Method (Recommended)**

Just run the main application:

```bash
python app.py
```

**✨ New Feature:** Models switch instantly without server restart!

### 🔧 **Alternative Methods**

You can also use the wrapper scripts (though they're no longer needed):

#### **Windows Users:**
```bash
run_app.bat
```

#### **All Platforms:**
```bash
python run_app.py
```

---

## 🔄 **Model Switching Guide**

### **✨ Instant Model Switching (New!)**

1. **Start the app** with `python app.py`
2. **Open browser** to `http://localhost:8000`
3. **Select model** from dropdown menu
4. **Click "Switch Model"** button
5. **Wait 2-10 seconds** - model switches instantly!
6. **Continue using** - no restart needed!

### **🎯 What Happens During Switch**

- **Server keeps running** - no interruption
- **Video stream continues** - minimal downtime
- **Model downloads** automatically if needed
- **Instant feedback** - see the switch in real-time

---

## 🎭 **Model Types to Try**

### **🎯 Detection Models** (Bounding boxes only)
- `yolo11n.pt` - Fastest, good for testing
- `yolo11s.pt` - Good balance of speed/accuracy
- `yolo11m.pt` - Better accuracy, slower

### **🎨 Segmentation Models** (Boxes + colored masks)
- `yolo11n-seg.pt` - Fast segmentation
- `yolo11s-seg.pt` - Better segmentation quality
- `yolo11m-seg.pt` - High quality segmentation

### **🤸 Pose Models** (Human pose keypoints)
- `yolo11n-pose.pt` - Fast pose detection
- `yolo11s-pose.pt` - Better pose accuracy

---

## 🛠️ **Troubleshooting**

### **Model Switch Takes Long**
- **Normal:** First-time downloads can take 1-5 minutes
- **Tip:** Try smaller models first (nano/small)
- **Watch terminal:** You'll see download progress

### **Camera Not Working**
- **Check:** Close other apps using webcam
- **Try:** Change `CAM_INDEX` in `app.py` to `1` or `2`

### **Model Switch Fails**
- **Check terminal:** Look for error messages
- **Try again:** Some downloads may timeout
- **Use smaller models:** If memory issues occur

---

## 🎉 **You're Ready!**

**Start with:** `python app.py`
**Open:** `http://localhost:8000`
**Enjoy:** Real-time YOLO detection with instant model switching!
