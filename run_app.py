#!/usr/bin/env python3
"""
Wrapper script for YOLO Detection Web App with automatic restart support
This script handles model switching by automatically restarting the server
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def run_app():
    """Run the main application with restart support"""
    print("🚀 YOLO Detection Web App - Auto-Restart Wrapper")
    print("=" * 60)
    print("📝 This wrapper handles automatic restarts for model switching")
    print("🔄 When you switch models, the app will restart automatically")
    print("🛑 Press Ctrl+C to stop completely")
    print("=" * 60)
    
    restart_count = 0
    max_restarts = 10  # Prevent infinite restart loops
    
    while restart_count < max_restarts:
        try:
            print(f"\n🔄 Starting application (attempt {restart_count + 1})")
            
            # Run the main app
            result = subprocess.run([sys.executable, "app.py"], 
                                  cwd=os.getcwd(),
                                  capture_output=False)
            
            exit_code = result.returncode
            
            if exit_code == 0:
                # Normal exit (Ctrl+C)
                print("\n✅ Application stopped normally")
                break
            elif exit_code == 42:
                # Special restart code
                print("\n🔄 Model switch requested - restarting...")
                restart_count += 1
                time.sleep(1)  # Brief pause before restart
                continue
            else:
                # Error exit
                print(f"\n❌ Application exited with error code: {exit_code}")
                if restart_count < 3:  # Try a few times for errors
                    print("🔄 Attempting restart...")
                    restart_count += 1
                    time.sleep(2)
                    continue
                else:
                    print("❌ Too many errors, stopping")
                    break
                    
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user (Ctrl+C)")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            if restart_count < 3:
                restart_count += 1
                time.sleep(2)
                continue
            else:
                break
    
    if restart_count >= max_restarts:
        print(f"\n❌ Maximum restart attempts ({max_restarts}) reached")
        print("🔧 Please check for issues and try again")
    
    print("\n👋 Goodbye!")

if __name__ == "__main__":
    # Check if app.py exists
    if not os.path.exists("app.py"):
        print("❌ Error: app.py not found in current directory")
        print("📁 Please run this script from the same folder as app.py")
        sys.exit(1)
    
    run_app()
