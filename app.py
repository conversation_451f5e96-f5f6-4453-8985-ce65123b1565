from flask import Flask, Response, render_template_string, request, jsonify, redirect, url_for
from ultralytics import YOLO
import cv2
import os
import sys
import glob
import threading
import time
import signal
from pathlib import Path
import datetime
from collections import deque
import psutil

# Configuration
DEFAULT_MODEL = "yolo11n-seg.pt"  # Default model
CAM_INDEX = 0                     # Change to 1/2 if needed
CONF = 0.35                      # Confidence threshold
IMG_SIZE = 640                   # Input size for YOLO (640 is good balance)
JPEG_QUALITY = 80                # JPEG compression quality

# Global variables
current_model_path = DEFAULT_MODEL
model_switch_lock = threading.Lock()
model_loading = False
detection_log = deque(maxlen=100)  # Keep last 100 detections
gpu_info = None

app = Flask(__name__)

def detect_gpu_info():
    """Detect GPU information and capabilities"""
    global gpu_info

    gpu_info = {
        "torch_cuda_available": False,
        "cuda_device_count": 0,
        "current_device": None,
        "device_name": "CPU",
        "gpu_memory_total": 0,
        "gpu_memory_used": 0,
        "gpu_utilization": 0,
        "driver_version": "N/A"
    }

    try:
        import torch
        gpu_info["torch_cuda_available"] = torch.cuda.is_available()

        if torch.cuda.is_available():
            gpu_info["cuda_device_count"] = torch.cuda.device_count()
            gpu_info["current_device"] = torch.cuda.current_device()
            gpu_info["device_name"] = torch.cuda.get_device_name()

            # Get memory info
            memory_info = torch.cuda.mem_get_info()
            gpu_info["gpu_memory_total"] = memory_info[1] // (1024**3)  # GB
            gpu_info["gpu_memory_used"] = (memory_info[1] - memory_info[0]) // (1024**3)  # GB

            # Try to get additional GPU info using GPUtil
            try:
                import GPUtil
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]  # First GPU
                    gpu_info["gpu_utilization"] = gpu.load * 100
                    gpu_info["gpu_memory_used"] = gpu.memoryUsed // 1024  # GB
                    gpu_info["gpu_memory_total"] = gpu.memoryTotal // 1024  # GB
                    gpu_info["driver_version"] = "Available"
            except ImportError:
                pass

    except ImportError:
        pass

    return gpu_info

def get_system_status():
    """Get current system status including GPU"""
    status = {
        "cpu_percent": psutil.cpu_percent(interval=1),
        "memory_percent": psutil.virtual_memory().percent,
        "gpu_info": gpu_info or detect_gpu_info()
    }

    # Update GPU utilization if available
    if gpu_info and gpu_info["torch_cuda_available"]:
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]
                status["gpu_info"]["gpu_utilization"] = gpu.load * 100
                status["gpu_info"]["gpu_memory_used"] = gpu.memoryUsed // 1024
        except:
            pass

    return status

def log_detection(detections, model_name):
    """Log detected objects with timestamp"""
    global detection_log

    if not detections or len(detections) == 0:
        return

    timestamp = datetime.datetime.now().strftime("%H:%M:%S")

    # Count detections by class
    detection_counts = {}
    for detection in detections:
        class_name = detection.get('name', 'unknown')
        confidence = detection.get('confidence', 0)
        if confidence > CONF:  # Only log if above confidence threshold
            detection_counts[class_name] = detection_counts.get(class_name, 0) + 1

    if detection_counts:
        log_entry = {
            "timestamp": timestamp,
            "model": model_name,
            "detections": detection_counts,
            "total_objects": sum(detection_counts.values())
        }
        detection_log.append(log_entry)

def get_detection_log():
    """Get recent detection log"""
    return list(detection_log)

def download_huggingface_model(repo_id):
    """Download a model from Hugging Face Hub"""
    try:
        # Try to import huggingface_hub
        try:
            from huggingface_hub import hf_hub_download, list_repo_files
        except ImportError:
            print("Installing huggingface_hub...")
            import subprocess
            subprocess.run([sys.executable, "-m", "pip", "install", "huggingface_hub"],
                          check=True, capture_output=True)
            from huggingface_hub import hf_hub_download, list_repo_files

        print(f"🤗 Downloading from Hugging Face: {repo_id}")

        # Create a folder for the model
        model_folder = repo_id.replace("/", "_")

        # List available files
        try:
            files = list_repo_files(repo_id)
            model_files = [f for f in files if f.endswith(('.pt', '.pth', '.onnx', '.engine', '.bin', '.safetensors'))]

            if not model_files:
                return False, "No compatible model files found in repository"

            # Download the main model files
            downloaded_files = []
            for filename in model_files[:3]:  # Limit to first 3 files to avoid huge downloads
                try:
                    local_path = hf_hub_download(
                        repo_id=repo_id,
                        filename=filename,
                        local_dir=f"./{model_folder}",
                        local_dir_use_symlinks=False
                    )
                    downloaded_files.append(filename)
                    print(f"✅ Downloaded {filename}")
                except Exception as e:
                    print(f"⚠️  Failed to download {filename}: {e}")
                    continue

            # Also try to download config files
            config_files = ["config.json", "preprocessor_config.json", "README.md"]
            for filename in config_files:
                try:
                    hf_hub_download(
                        repo_id=repo_id,
                        filename=filename,
                        local_dir=f"./{model_folder}",
                        local_dir_use_symlinks=False
                    )
                    print(f"✅ Downloaded {filename}")
                except:
                    pass  # Config files are optional

            if downloaded_files:
                return True, f"Downloaded {len(downloaded_files)} files to ./{model_folder}/"
            else:
                return False, "No files were successfully downloaded"

        except Exception as e:
            return False, f"Failed to access repository: {str(e)}"

    except Exception as e:
        return False, f"Download error: {str(e)}"

def find_yolo_models():
    """Find all available YOLO models in current directory and common locations"""
    models = []

    # Common model patterns
    model_patterns = [
        "yolo*.pt",
        "yolo*.onnx",
        "yolo*.engine",
        "*.pt",
        "*.onnx",
        "*.engine",
        "*.bin",           # Hugging Face models
        "*.safetensors",   # Hugging Face safe tensors
        "pytorch_model.bin" # Standard HF model name
    ]

    # Search locations
    search_paths = [
        ".",  # Current directory
        os.path.expanduser("~/.ultralytics"),  # Ultralytics cache
        os.path.expanduser("~/Downloads"),     # Downloads folder
        "./models",                            # Models subdirectory
        "./weights",                           # Weights subdirectory
        "./yolos-tiny",                        # YOLOS models
        "./yolos-small",
        "./yolos-base",
        "./custom_models",                     # Custom models folder
    ]

    # Known YOLO model names (will auto-download if selected)
    known_models = [
        "yolo11n.pt", "yolo11s.pt", "yolo11m.pt", "yolo11l.pt", "yolo11x.pt",
        "yolo11n-seg.pt", "yolo11s-seg.pt", "yolo11m-seg.pt", "yolo11l-seg.pt", "yolo11x-seg.pt",
        "yolo11n-pose.pt", "yolo11s-pose.pt", "yolo11m-pose.pt", "yolo11l-pose.pt", "yolo11x-pose.pt",
        "yolov8n.pt", "yolov8s.pt", "yolov8m.pt", "yolov8l.pt", "yolov8x.pt",
        "yolov8n-seg.pt", "yolov8s-seg.pt", "yolov8m-seg.pt", "yolov8l-seg.pt", "yolov8x-seg.pt",
        "yolov9c.pt", "yolov9e.pt", "yolov10n.pt", "yolov10s.pt", "yolov10m.pt", "yolov10l.pt", "yolov10x.pt"
    ]

    # Search for existing model files (including recursive search)
    for search_path in search_paths:
        if os.path.exists(search_path):
            # Search in the directory itself
            for pattern in model_patterns:
                files = glob.glob(os.path.join(search_path, pattern))
                for file_path in files:
                    if os.path.isfile(file_path):
                        abs_path = os.path.abspath(file_path)
                        rel_path = os.path.relpath(abs_path)
                        file_size = os.path.getsize(abs_path)

                        # Skip very small files (likely not real models)
                        if file_size > 1024 * 1024:  # > 1MB
                            # Check compatibility before adding
                            compatible, reason = check_model_compatibility(abs_path)
                            models.append({
                                "name": os.path.basename(file_path),
                                "path": rel_path,
                                "size_mb": round(file_size / (1024 * 1024), 1),
                                "type": "local",
                                "location": search_path,
                                "compatible": compatible,
                                "compatibility_reason": reason
                            })

            # Also search recursively in subdirectories (up to 2 levels deep)
            if search_path == ".":  # Only do recursive search for current directory
                for pattern in model_patterns:
                    # Search one level deep
                    files = glob.glob(os.path.join(search_path, "*", pattern))
                    for file_path in files:
                        if os.path.isfile(file_path):
                            abs_path = os.path.abspath(file_path)
                            rel_path = os.path.relpath(abs_path)
                            file_size = os.path.getsize(abs_path)

                            # Skip very small files (likely not real models)
                            if file_size > 1024 * 1024:  # > 1MB
                                # Check compatibility before adding
                                compatible, reason = check_model_compatibility(abs_path)

                                # Create a more descriptive name for models in folders
                                folder_name = os.path.basename(os.path.dirname(file_path))
                                model_name = os.path.basename(file_path)
                                display_name = f"{folder_name}/{model_name}"

                                models.append({
                                    "name": display_name,
                                    "path": rel_path,
                                    "size_mb": round(file_size / (1024 * 1024), 1),
                                    "type": "local",
                                    "location": f"./{folder_name}",
                                    "compatible": compatible,
                                    "compatibility_reason": reason
                                })

                    # Search two levels deep
                    files = glob.glob(os.path.join(search_path, "*", "*", pattern))
                    for file_path in files:
                        if os.path.isfile(file_path):
                            abs_path = os.path.abspath(file_path)
                            rel_path = os.path.relpath(abs_path)
                            file_size = os.path.getsize(abs_path)

                            # Skip very small files (likely not real models)
                            if file_size > 1024 * 1024:  # > 1MB
                                # Check compatibility before adding
                                compatible, reason = check_model_compatibility(abs_path)

                                # Create a more descriptive name for models in nested folders
                                path_parts = rel_path.split(os.sep)
                                if len(path_parts) >= 3:
                                    folder_path = "/".join(path_parts[:-1])
                                    model_name = path_parts[-1]
                                    display_name = f"{folder_path}/{model_name}"
                                else:
                                    display_name = os.path.basename(file_path)

                                models.append({
                                    "name": display_name,
                                    "path": rel_path,
                                    "size_mb": round(file_size / (1024 * 1024), 1),
                                    "type": "local",
                                    "location": os.path.dirname(rel_path),
                                    "compatible": compatible,
                                    "compatibility_reason": reason
                                })

    # Add known models that can be auto-downloaded
    for model_name in known_models:
        # Only add if not already found locally
        if not any(m["name"] == model_name for m in models):
            models.append({
                "name": model_name,
                "path": model_name,
                "size_mb": "Auto-download",
                "type": "remote",
                "location": "Ultralytics Hub",
                "compatible": True,  # Ultralytics models are always compatible
                "compatibility_reason": "Ultralytics official model"
            })

    # Remove duplicates and separate compatible/incompatible models
    seen = set()
    compatible_models = []
    incompatible_models = []

    for model in models:
        if model["name"] not in seen:
            seen.add(model["name"])
            if model.get("compatible", True):  # Default to compatible for remote models
                compatible_models.append(model)
            else:
                incompatible_models.append(model)

    # Use only compatible models for the main list
    unique_models = compatible_models

    # Sort: newly downloaded models first, then local models, then remote
    # Check file modification time to identify recently downloaded models
    for model in unique_models:
        if model["type"] == "local":
            try:
                file_path = model["path"]
                if os.path.exists(file_path):
                    # Check if file was modified in the last hour (newly downloaded)
                    import time
                    file_time = os.path.getmtime(file_path)
                    current_time = time.time()
                    if current_time - file_time < 3600:  # 1 hour
                        model["recently_downloaded"] = True
                    else:
                        model["recently_downloaded"] = False
                else:
                    model["recently_downloaded"] = False
            except:
                model["recently_downloaded"] = False
        else:
            model["recently_downloaded"] = False

    # Sort: recently downloaded first, then local models, then remote, then by name
    unique_models.sort(key=lambda x: (
        not x.get("recently_downloaded", False),  # Recently downloaded first
        x["type"] == "remote",                    # Local before remote
        x["name"]                                 # Alphabetical
    ))

    return unique_models

def check_model_compatibility(model_path):
    """Check if a model is compatible with Ultralytics YOLO"""
    try:
        # Quick compatibility check based on file extension and content
        if not os.path.exists(model_path):
            return False, "File does not exist"

        file_ext = os.path.splitext(model_path)[1].lower()

        # Check file extension
        compatible_extensions = ['.pt', '.onnx', '.engine']
        if file_ext not in compatible_extensions:
            return False, f"Unsupported file format: {file_ext}. Supported: {', '.join(compatible_extensions)}"

        # For .pt files, try a quick load test
        if file_ext == '.pt':
            try:
                # Try to create YOLO model (this will fail quickly for incompatible models)
                temp_model = YOLO(model_path)
                # If we get here, the model is compatible
                del temp_model
                return True, "Compatible"
            except Exception as e:
                error_msg = str(e).lower()
                if "huggingface" in error_msg or "transformers" in error_msg or "safetensors" in error_msg:
                    return False, "Hugging Face Transformers model (not compatible with Ultralytics)"
                elif "pytorch_model.bin" in error_msg:
                    return False, "PyTorch model file (not compatible with Ultralytics)"
                else:
                    return False, f"Model loading error: {str(e)[:100]}..."

        # For other formats, assume compatible (will be tested during actual loading)
        return True, "Compatible"

    except Exception as e:
        return False, f"Compatibility check error: {str(e)}"

def get_model_info(model_path):
    """Get information about a YOLO model"""
    try:
        # First check compatibility
        compatible, reason = check_model_compatibility(model_path)
        if not compatible:
            return {"task": "Unknown", "classes": "Unknown", "input_size": 640, "error": reason, "compatible": False}

        # Try to load model to get info
        temp_model = YOLO(model_path)
        info = {
            "task": getattr(temp_model, 'task', 'detect'),
            "classes": len(temp_model.names) if hasattr(temp_model, 'names') else 'Unknown',
            "input_size": getattr(temp_model, 'imgsz', 640),
            "compatible": True
        }
        del temp_model  # Clean up
        return info
    except Exception as e:
        return {"task": "Unknown", "classes": "Unknown", "input_size": 640, "error": str(e), "compatible": False}

# HTML template for the web page
PAGE_TEMPLATE = """
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Real-time YOLO Detection</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      margin: 0;
      background: #0b0b0b;
      color: #eee;
      font-family: system-ui, -apple-system, 'Segoe UI', Roboto, sans-serif;
    }
    header {
      padding: 12px 16px;
      font-weight: 600;
      background: #1a1a1a;
      border-bottom: 1px solid #333;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 16px;
    }
    .header-title {
      font-size: 18px;
    }
    .controls {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }
    .model-selector {
      display: flex;
      align-items: center;
      gap: 8px;
      background: #2a2a2a;
      padding: 8px 12px;
      border-radius: 6px;
      border: 1px solid #444;
    }
    .model-selector label {
      font-size: 14px;
      font-weight: 500;
    }
    .model-selector select {
      background: #333;
      color: #eee;
      border: 1px solid #555;
      border-radius: 4px;
      padding: 6px 8px;
      font-size: 14px;
      min-width: 200px;
    }
    .model-selector select:focus {
      outline: none;
      border-color: #007acc;
    }
    .btn {
      background: #007acc;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background 0.2s;
    }
    .btn:hover {
      background: #005a9e;
    }
    .btn:disabled {
      background: #555;
      cursor: not-allowed;
    }
    .btn.danger {
      background: #dc3545;
    }
    .btn.danger:hover {
      background: #c82333;
    }
    .wrap {
      display: flex;
      justify-content: center;
      padding: 16px;
    }
    img {
      width: min(96vw, 1280px);
      height: auto;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.5);
    }
    .info-panel {
      max-width: 1280px;
      margin: 0 auto;
      padding: 0 16px;
    }
    .model-info {
      background: #1a1a1a;
      border: 1px solid #333;
      border-radius: 8px;
      padding: 16px;
      margin: 16px 0;
    }
    .model-info h3 {
      margin: 0 0 12px 0;
      color: #007acc;
      font-size: 16px;
    }
    .model-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;
      font-size: 14px;
    }
    .model-details div {
      background: #2a2a2a;
      padding: 8px 12px;
      border-radius: 4px;
    }
    .model-details strong {
      color: #ffa500;
    }
    .status {
      text-align: center;
      padding: 16px;
      background: #1a1a1a;
      margin: 16px;
      border-radius: 8px;
      border: 1px solid #333;
    }
    .loading {
      color: #ffa500;
    }
    .error {
      color: #ff6b6b;
    }
    .success {
      color: #28a745;
    }
    .meta {
      text-align: center;
      opacity: 0.7;
      padding: 8px;
      font-size: 14px;
      max-width: 600px;
      margin: 0 auto;
    }
    .status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;
      max-width: 1280px;
      margin: 16px auto;
      padding: 0 16px;
    }
    .status-card {
      background: #1a1a1a;
      border: 1px solid #333;
      border-radius: 8px;
      padding: 16px;
    }
    .status-card h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      font-size: 14px;
    }
    .status-value {
      font-weight: 600;
    }
    .gpu-enabled {
      color: #28a745;
    }
    .gpu-disabled {
      color: #ffa500;
    }
    .detection-log {
      max-height: 300px;
      overflow-y: auto;
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }
    .detection-entry {
      padding: 4px 8px;
      border-bottom: 1px solid #333;
      display: flex;
      justify-content: space-between;
    }
    .detection-entry:hover {
      background: #2a2a2a;
    }
    .detection-time {
      color: #007acc;
      font-weight: 600;
    }
    .detection-objects {
      color: #ffa500;
    }
    .download-section {
      background: #1a1a1a;
      border: 1px solid #333;
      border-radius: 8px;
      padding: 16px;
      margin: 16px auto;
      max-width: 800px;
    }
    .download-section h3 {
      margin: 0 0 12px 0;
      color: #007acc;
      font-size: 16px;
    }
    .url-input-group {
      display: flex;
      gap: 8px;
      align-items: center;
      flex-wrap: wrap;
    }
    .url-input {
      flex: 1;
      min-width: 300px;
      padding: 8px 12px;
      background: #2a2a2a;
      border: 1px solid #555;
      border-radius: 4px;
      color: #eee;
      font-size: 14px;
    }
    .url-input:focus {
      outline: none;
      border-color: #007acc;
    }
    .url-input::placeholder {
      color: #888;
    }
    .download-btn {
      background: #28a745;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background 0.2s;
    }
    .download-btn:hover {
      background: #218838;
    }
    .download-btn:disabled {
      background: #555;
      cursor: not-allowed;
    }
    .download-status {
      margin-top: 8px;
      padding: 8px;
      border-radius: 4px;
      font-size: 14px;
      display: none;
    }
    .download-status.success {
      background: #1a4d2e;
      color: #28a745;
      border: 1px solid #28a745;
    }
    .download-status.error {
      background: #4d1a1a;
      color: #ff6b6b;
      border: 1px solid #ff6b6b;
    }
    .download-status.loading {
      background: #4d3a1a;
      color: #ffa500;
      border: 1px solid #ffa500;
    }
    @media (max-width: 768px) {
      header {
        flex-direction: column;
        align-items: stretch;
      }
      .controls {
        justify-content: center;
      }
      .model-selector {
        flex-direction: column;
        align-items: stretch;
      }
      .model-selector select {
        min-width: auto;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="header-title">🎯 YOLO Detection — Real-time Webcam</div>
    <div class="controls">
      <div class="model-selector">
        <label for="modelSelect">Model:</label>
        <select id="modelSelect" onchange="updateModelInfo()">
          {% for model in models %}
          <option value="{{ model.path }}"
                  data-type="{{ model.type }}"
                  data-size="{{ model.size_mb }}"
                  data-location="{{ model.location }}"
                  data-recent="{{ model.get('recently_downloaded', False) }}"
                  {% if model.path == current_model %}selected{% endif %}>
            {% if model.get('recently_downloaded', False) %}🆕 {% endif %}{{ model.name }} ({{ model.size_mb }}{% if model.type == 'remote' %}{% else %}MB{% endif %})
          </option>
          {% endfor %}
        </select>
      </div>
      <button class="btn" onclick="switchModel()" id="switchBtn">Switch Model</button>
      <button class="btn" onclick="refreshModels()">Refresh</button>
      <button class="btn" onclick="showDownloadInfo()" style="background:#28a745;">Download More</button>
    </div>
  </header>

  <div class="wrap">
    <img src="/video" alt="Live webcam stream with YOLO detection"
         onerror="this.style.display='none'; document.getElementById('error').style.display='block';">
  </div>

  <div id="error" class="status error" style="display:none;">
    ❌ Video stream unavailable. Check if your webcam is accessible and not being used by another application.
  </div>

  <div id="loading" class="status loading" style="display:none;">
    🔄 Switching model...
    <br><small>This may take a few seconds for model download and loading.</small>
  </div>

  <div id="success" class="status success" style="display:none;">
    ✅ Model switched successfully!
  </div>

  <div id="info-panel" class="status" style="background:#2a2a2a; border-color:#555;">
    💡 <strong>Model Switching:</strong>
    <br>• Models switch instantly without server restart
    <br>• First-time downloads may take longer
    <br>• Video stream continues during model switch
  </div>

  <div id="download-info" class="status" style="display:none; background:#1a4d2e; border-color:#28a745;">
    📥 <strong>Download More Models:</strong>
    <br>• Use the download section below to paste Hugging Face URLs
    <br>• Run <code>python download_models.py</code> for interactive download
    <br>• Run <code>python download_yolos.py</code> for YOLOS models
    <br>• See <code>MODEL_DOWNLOAD_GUIDE.md</code> for detailed instructions
    <br>• Place custom models in the project folder and refresh
  </div>

  <div class="download-section">
    <h3>📥 Download Model from URL</h3>
    <div class="url-input-group">
      <input type="text"
             class="url-input"
             id="modelUrl"
             placeholder="Paste Hugging Face URL (e.g., https://huggingface.co/hustvl/yolos-small)"
             value="">
      <button class="download-btn" onclick="downloadFromUrl()" id="downloadBtn">
        Download Model
      </button>
    </div>
    <div id="downloadStatus" class="download-status"></div>
    <div style="margin-top: 8px; font-size: 12px; opacity: 0.7;">
      💡 Supported: Hugging Face model URLs • After download, refresh the page to see the new model
    </div>
  </div>

  <div class="info-panel">
    <div class="model-info">
      <h3>📊 Current Model Information</h3>
      <div class="model-details">
        <div><strong>Name:</strong> <span id="currentModelName">{{ current_model_name }}</span></div>
        <div><strong>Type:</strong> <span id="currentModelType">{{ current_model_type }}</span></div>
        <div><strong>Size:</strong> <span id="currentModelSize">{{ current_model_size }}</span></div>
        <div><strong>Location:</strong> <span id="currentModelLocation">{{ current_model_location }}</span></div>
      </div>
    </div>

    <div class="status-grid">
      <div class="status-card">
        <h3>🖥️ System Status</h3>
        <div class="status-item">
          <span>CPU Usage:</span>
          <span class="status-value" id="cpuUsage">Loading...</span>
        </div>
        <div class="status-item">
          <span>Memory Usage:</span>
          <span class="status-value" id="memoryUsage">Loading...</span>
        </div>
        <div class="status-item">
          <span>Processing Device:</span>
          <span class="status-value" id="processingDevice">Loading...</span>
        </div>
      </div>

      <div class="status-card">
        <h3>🎮 GPU Status</h3>
        <div class="status-item">
          <span>CUDA Available:</span>
          <span class="status-value" id="cudaStatus">Loading...</span>
        </div>
        <div class="status-item">
          <span>GPU Name:</span>
          <span class="status-value" id="gpuName">Loading...</span>
        </div>
        <div class="status-item">
          <span>GPU Memory:</span>
          <span class="status-value" id="gpuMemory">Loading...</span>
        </div>
        <div class="status-item">
          <span>GPU Utilization:</span>
          <span class="status-value" id="gpuUtilization">Loading...</span>
        </div>
      </div>

      <div class="status-card">
        <h3>🔍 Detection Log</h3>
        <div class="detection-log" id="detectionLog">
          <div style="text-align: center; padding: 20px; opacity: 0.7;">
            Waiting for detections...
          </div>
        </div>
      </div>
    </div>

    <div class="meta">
      This page shows your webcam feed with real-time object detection using YOLO models.
      <br>Select different models from the dropdown to compare performance and accuracy.
      <br><strong>Tip:</strong> Segmentation models (ending in -seg) show colored masks, while detection models show only bounding boxes.
    </div>
  </div>

  <script>
    function updateModelInfo() {
      const select = document.getElementById('modelSelect');
      const option = select.options[select.selectedIndex];

      document.getElementById('currentModelName').textContent = option.text.split(' (')[0];
      document.getElementById('currentModelType').textContent = option.dataset.type;
      document.getElementById('currentModelSize').textContent = option.dataset.size + (option.dataset.type === 'remote' ? '' : 'MB');
      document.getElementById('currentModelLocation').textContent = option.dataset.location;
    }

    function switchModel() {
      const select = document.getElementById('modelSelect');
      const selectedModel = select.value;
      const btn = document.getElementById('switchBtn');

      if (!selectedModel) return;

      // Show loading state
      document.getElementById('loading').style.display = 'block';
      document.getElementById('error').style.display = 'none';
      document.getElementById('success').style.display = 'none';
      btn.disabled = true;
      btn.textContent = 'Switching...';

      // Send request to switch model
      fetch('/switch_model', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({model_path: selectedModel})
      })
      .then(response => response.json())
      .then(data => {
        document.getElementById('loading').style.display = 'none';
        btn.disabled = false;
        btn.textContent = 'Switch Model';

        if (data.success) {
          document.getElementById('success').innerHTML =
            '✅ Model switched successfully to: <strong>' + data.new_model + '</strong>';
          document.getElementById('success').style.display = 'block';
          document.getElementById('error').style.display = 'none';

          // Update the current model info
          updateModelInfo();

          // Hide success message after 3 seconds
          setTimeout(() => {
            document.getElementById('success').style.display = 'none';
          }, 3000);

        } else {
          throw new Error(data.error || 'Unknown error');
        }
      })
      .catch(error => {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').innerHTML = '❌ Error switching model: ' + error.message;
        document.getElementById('error').style.display = 'block';
        btn.disabled = false;
        btn.textContent = 'Switch Model';
      });
    }

    function refreshModels() {
      window.location.reload();
    }

    function showDownloadInfo() {
      const downloadDiv = document.getElementById('download-info');
      if (downloadDiv.style.display === 'none') {
        downloadDiv.style.display = 'block';
        setTimeout(() => {
          downloadDiv.style.display = 'none';
        }, 10000); // Hide after 10 seconds
      } else {
        downloadDiv.style.display = 'none';
      }
    }



    // Initialize model info on page load
    updateModelInfo();

    // Update system status periodically
    function updateSystemStatus() {
      fetch('/gpu_status')
        .then(response => response.json())
        .then(data => {
          // Update system status
          document.getElementById('cpuUsage').textContent = data.cpu_percent.toFixed(1) + '%';
          document.getElementById('memoryUsage').textContent = data.memory_percent.toFixed(1) + '%';

          // Update GPU status
          const gpuInfo = data.gpu_info;
          const cudaStatus = document.getElementById('cudaStatus');
          const gpuName = document.getElementById('gpuName');
          const gpuMemory = document.getElementById('gpuMemory');
          const gpuUtilization = document.getElementById('gpuUtilization');
          const processingDevice = document.getElementById('processingDevice');

          if (gpuInfo.torch_cuda_available) {
            cudaStatus.textContent = '✅ Yes';
            cudaStatus.className = 'status-value gpu-enabled';
            gpuName.textContent = gpuInfo.device_name;
            gpuMemory.textContent = gpuInfo.gpu_memory_used + '/' + gpuInfo.gpu_memory_total + ' GB';
            gpuUtilization.textContent = gpuInfo.gpu_utilization.toFixed(1) + '%';
            processingDevice.textContent = '🎮 GPU (' + gpuInfo.device_name + ')';
            processingDevice.className = 'status-value gpu-enabled';
          } else {
            cudaStatus.textContent = '❌ No';
            cudaStatus.className = 'status-value gpu-disabled';
            gpuName.textContent = 'No GPU detected';
            gpuMemory.textContent = 'N/A';
            gpuUtilization.textContent = 'N/A';
            processingDevice.textContent = '🖥️ CPU Only';
            processingDevice.className = 'status-value gpu-disabled';
          }
        })
        .catch(error => {
          console.error('Error updating system status:', error);
        });
    }

    // Update detection log
    function updateDetectionLog() {
      fetch('/detection_log')
        .then(response => response.json())
        .then(data => {
          const logContainer = document.getElementById('detectionLog');

          if (data.detections.length === 0) {
            logContainer.innerHTML = '<div style="text-align: center; padding: 20px; opacity: 0.7;">Waiting for detections...</div>';
            return;
          }

          // Show last 10 detections
          const recentDetections = data.detections.slice(-10).reverse();
          let logHtml = '';

          recentDetections.forEach(detection => {
            const objectsList = Object.entries(detection.detections)
              .map(([name, count]) => count > 1 ? `${name}(${count})` : name)
              .join(', ');

            logHtml += `
              <div class="detection-entry">
                <span class="detection-time">${detection.timestamp}</span>
                <span class="detection-objects">${objectsList}</span>
              </div>
            `;
          });

          logContainer.innerHTML = logHtml;
        })
        .catch(error => {
          console.error('Error updating detection log:', error);
        });
    }

    // Download model from URL
    function downloadFromUrl() {
      const urlInput = document.getElementById('modelUrl');
      const downloadBtn = document.getElementById('downloadBtn');
      const statusDiv = document.getElementById('downloadStatus');

      const url = urlInput.value.trim();
      if (!url) {
        showDownloadStatus('Please enter a URL', 'error');
        return;
      }

      // Validate URL format
      if (!url.includes('huggingface.co/')) {
        showDownloadStatus('Please enter a valid Hugging Face URL', 'error');
        return;
      }

      // Show loading state
      downloadBtn.disabled = true;
      downloadBtn.textContent = 'Downloading...';
      showDownloadStatus('Starting download... This may take a few minutes.', 'loading');

      // Send download request
      fetch('/download_from_url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({url: url})
      })
      .then(response => response.json())
      .then(data => {
        downloadBtn.disabled = false;
        downloadBtn.textContent = 'Download Model';

        if (data.success) {
          showDownloadStatus(`✅ ${data.message}`, 'success');
          urlInput.value = ''; // Clear input

          // Show refresh reminder
          setTimeout(() => {
            showDownloadStatus('✅ Download complete! Refresh the page to see the new model.', 'success');
          }, 2000);
        } else {
          showDownloadStatus(`❌ ${data.error}`, 'error');
        }
      })
      .catch(error => {
        downloadBtn.disabled = false;
        downloadBtn.textContent = 'Download Model';
        showDownloadStatus(`❌ Download failed: ${error.message}`, 'error');
      });
    }

    function showDownloadStatus(message, type) {
      const statusDiv = document.getElementById('downloadStatus');
      statusDiv.textContent = message;
      statusDiv.className = `download-status ${type}`;
      statusDiv.style.display = 'block';

      // Auto-hide after 10 seconds for non-success messages
      if (type !== 'success') {
        setTimeout(() => {
          statusDiv.style.display = 'none';
        }, 10000);
      }
    }

    // Update status every 2 seconds
    updateSystemStatus();
    updateDetectionLog();
    setInterval(updateSystemStatus, 2000);
    setInterval(updateDetectionLog, 1000);
  </script>
</body>
</html>
"""

# Initialize YOLO model and webcam
model = None
cap = None

def initialize_model(model_path=None):
    """Initialize the YOLO model"""
    global model, current_model_path

    if model_path:
        current_model_path = model_path

    try:
        print(f"Loading YOLO model: {current_model_path}")
        new_model = YOLO(current_model_path)

        # Only update global model if successful
        model = new_model
        print(f"✅ Model loaded successfully: {current_model_path}")

        # Print model info
        if hasattr(model, 'names'):
            print(f"   Classes: {len(model.names)} ({', '.join(list(model.names.values())[:5])}{'...' if len(model.names) > 5 else ''})")
        if hasattr(model, 'task'):
            print(f"   Task: {model.task}")

        return True
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def switch_model_in_process(new_model_path):
    """Switch model without restarting the server"""
    global model, current_model_path, model_loading

    with model_switch_lock:
        if model_loading:
            return False, "Model switch already in progress"

        model_loading = True

        try:
            print(f"\n🔄 Switching model from {current_model_path} to {new_model_path}")

            # Validate model path (handle both forward and backward slashes)
            models = find_yolo_models()
            normalized_new_path = new_model_path.replace("/", os.sep).replace("\\", os.sep)

            valid_model = False
            selected_model = None
            for m in models:
                normalized_model_path = m["path"].replace("/", os.sep).replace("\\", os.sep)
                if normalized_model_path == normalized_new_path:
                    valid_model = True
                    selected_model = m
                    # Use the actual path from the model list
                    new_model_path = m["path"]
                    break

            if not valid_model:
                print(f"❌ Model path not found: {new_model_path}")
                print(f"   Available paths: {[m['path'] for m in models[:5]]}...")
                return False, "Model not found in available models list"

            # Check if the model is compatible
            if not selected_model.get("compatible", True):
                reason = selected_model.get("compatibility_reason", "Unknown compatibility issue")
                print(f"❌ Model is not compatible: {reason}")
                return False, f"Incompatible model: {reason}"

            # Load new model
            old_model_path = current_model_path
            if initialize_model(new_model_path):
                print(f"✅ Successfully switched from {old_model_path} to {current_model_path}")
                return True, f"Model switched to {new_model_path}"
            else:
                # Revert to old model if new one failed
                current_model_path = old_model_path
                return False, f"Failed to load {new_model_path}, keeping {old_model_path}"

        except Exception as e:
            return False, f"Error during model switch: {str(e)}"
        finally:
            model_loading = False

def initialize_camera():
    """Initialize the webcam"""
    global cap
    try:
        print(f"Initializing camera {CAM_INDEX}...")
        cap = cv2.VideoCapture(CAM_INDEX)
        
        if not cap.isOpened():
            print(f"❌ Cannot open camera {CAM_INDEX}")
            return False
            
        # Set camera properties
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        # Test read
        ret, frame = cap.read()
        if not ret:
            print("❌ Cannot read from camera")
            return False
            
        print(f"✅ Camera initialized successfully")
        print(f"   Resolution: {int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))}x{int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}")
        return True
    except Exception as e:
        print(f"❌ Error initializing camera: {e}")
        return False

@app.route("/")
def index():
    """Serve the main page"""
    models = find_yolo_models()

    # Find current model info
    current_model_info = next((m for m in models if m["path"] == current_model_path), None)
    if not current_model_info:
        current_model_info = {
            "name": os.path.basename(current_model_path),
            "type": "unknown",
            "size_mb": "Unknown",
            "location": "Unknown"
        }

    return render_template_string(PAGE_TEMPLATE,
                                models=models,
                                current_model=current_model_path,
                                current_model_name=current_model_info["name"],
                                current_model_type=current_model_info["type"],
                                current_model_size=current_model_info["size_mb"],
                                current_model_location=current_model_info["location"])

def generate_frames():
    """Generate video frames with YOLO inference"""
    global cap, model, model_loading

    if cap is None:
        return
    
    while True:
        success, frame = cap.read()
        if not success:
            print("❌ Failed to read frame from camera")
            break
        
        try:
            # Check if model is available and not being switched
            if model is None or model_loading:
                # Show a placeholder frame or skip this frame
                continue

            # Run YOLO inference
            # Determine if we should filter for persons only
            filter_persons = True  # Default to person detection

            # Check if model supports person detection (class 0)
            if hasattr(model, 'names') and model.names:
                if 0 in model.names and 'person' in model.names[0].lower():
                    # Standard COCO model with person as class 0
                    classes_filter = [0]
                elif any('person' in name.lower() for name in model.names.values()):
                    # Find person class in custom models
                    person_classes = [k for k, v in model.names.items() if 'person' in v.lower()]
                    classes_filter = person_classes if person_classes else None
                else:
                    # No person class found, detect all objects
                    classes_filter = None
                    filter_persons = False
            else:
                classes_filter = None
                filter_persons = False

            # Run inference
            if filter_persons and classes_filter:
                results = model.predict(
                    source=frame,
                    imgsz=IMG_SIZE,
                    conf=CONF,
                    classes=classes_filter,
                    verbose=False
                )
            else:
                results = model.predict(
                    source=frame,
                    imgsz=IMG_SIZE,
                    conf=CONF,
                    verbose=False
                )

            # Draw annotations (boxes + masks if segmentation model)
            annotated_frame = results[0].plot()

            # Log detections
            if results[0].boxes is not None and len(results[0].boxes) > 0:
                detections = []
                for box in results[0].boxes:
                    if hasattr(box, 'cls') and hasattr(box, 'conf'):
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        class_name = model.names.get(class_id, f"class_{class_id}")
                        detections.append({
                            "name": class_name,
                            "confidence": confidence,
                            "class_id": class_id
                        })

                if detections:
                    log_detection(detections, os.path.basename(current_model_path))
            
            # Encode frame as JPEG
            ret, buffer = cv2.imencode(
                '.jpg', 
                annotated_frame, 
                [int(cv2.IMWRITE_JPEG_QUALITY), JPEG_QUALITY]
            )
            
            if not ret:
                continue
                
            frame_bytes = buffer.tobytes()
            
            # Yield frame in MJPEG format
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                   
        except Exception as e:
            print(f"❌ Error processing frame: {e}")
            continue

@app.route("/video")
def video_feed():
    """Video streaming route"""
    return Response(
        generate_frames(),
        mimetype='multipart/x-mixed-replace; boundary=frame'
    )

@app.route("/switch_model", methods=["POST"])
def switch_model():
    """Switch to a different YOLO model without restarting server"""
    try:
        data = request.get_json()
        new_model_path = data.get("model_path")

        if not new_model_path:
            return jsonify({"success": False, "error": "No model path provided"})

        if new_model_path == current_model_path:
            return jsonify({"success": True, "message": f"Already using {new_model_path}"})

        # Switch model in-process
        success, message = switch_model_in_process(new_model_path)

        return jsonify({
            "success": success,
            "message": message,
            "new_model": current_model_path if success else None
        })

    except Exception as e:
        return jsonify({"success": False, "error": str(e)})

@app.route("/models")
def get_models():
    """Get list of available models"""
    models = find_yolo_models()
    return jsonify({
        "models": models,
        "current_model": current_model_path
    })

@app.route("/incompatible_models")
def get_incompatible_models():
    """Get list of incompatible models for debugging"""
    all_models = []

    # Get all models including incompatible ones
    search_paths = [
        ".",
        os.path.expanduser("~/.ultralytics"),
        os.path.expanduser("~/Downloads"),
        "./models",
        "./weights",
        "./yolos-tiny",
        "./yolos-small",
        "./yolos-base",
        "./custom_models",
    ]

    model_patterns = [
        "yolo*.pt", "yolo*.onnx", "yolo*.engine",
        "*.pt", "*.onnx", "*.engine", "*.bin", "*.safetensors", "pytorch_model.bin"
    ]

    for search_path in search_paths:
        if os.path.exists(search_path):
            for pattern in model_patterns:
                files = glob.glob(os.path.join(search_path, pattern))
                for file_path in files:
                    if os.path.isfile(file_path):
                        abs_path = os.path.abspath(file_path)
                        rel_path = os.path.relpath(abs_path)
                        file_size = os.path.getsize(abs_path)

                        if file_size > 1024 * 1024:  # > 1MB
                            compatible, reason = check_model_compatibility(abs_path)
                            all_models.append({
                                "name": os.path.basename(file_path),
                                "path": rel_path,
                                "size_mb": round(file_size / (1024 * 1024), 1),
                                "compatible": compatible,
                                "compatibility_reason": reason
                            })

    compatible_models = [m for m in all_models if m["compatible"]]
    incompatible_models = [m for m in all_models if not m["compatible"]]

    return jsonify({
        "compatible_count": len(compatible_models),
        "incompatible_count": len(incompatible_models),
        "incompatible_models": incompatible_models
    })

@app.route("/status")
def status():
    """Health check endpoint"""
    return jsonify({
        "model_loaded": model is not None,
        "camera_available": cap is not None and cap.isOpened(),
        "current_model": current_model_path,
        "camera_index": CAM_INDEX,
        "model_loading": model_loading,
        "model_info": {
            "task": getattr(model, 'task', 'unknown') if model else 'none',
            "classes": len(model.names) if model and hasattr(model, 'names') else 0
        },
        "system_status": get_system_status()
    })

@app.route("/gpu_status")
def gpu_status():
    """Get GPU status and information"""
    return jsonify(get_system_status())

@app.route("/detection_log")
def detection_log_endpoint():
    """Get recent detection log"""
    return jsonify({
        "detections": get_detection_log(),
        "total_entries": len(detection_log)
    })

@app.route("/download_from_url", methods=["POST"])
def download_from_url():
    """Download model from Hugging Face URL"""
    try:
        data = request.get_json()
        url = data.get("url", "").strip()

        if not url:
            return jsonify({"success": False, "error": "No URL provided"})

        # Validate Hugging Face URL
        if "huggingface.co/" not in url:
            return jsonify({"success": False, "error": "Please provide a valid Hugging Face URL"})

        # Parse the URL to get repo_id
        try:
            # Extract repo_id from URL like https://huggingface.co/hustvl/yolos-small
            parts = url.replace("https://", "").replace("http://", "").split("/")
            if len(parts) >= 3 and parts[0] == "huggingface.co":
                repo_id = "/".join(parts[1:3])  # e.g., "hustvl/yolos-small"
            else:
                return jsonify({"success": False, "error": "Invalid Hugging Face URL format"})
        except Exception as e:
            return jsonify({"success": False, "error": f"Failed to parse URL: {str(e)}"})

        # Start download in a separate thread to avoid blocking
        def download_model():
            try:
                success, message = download_huggingface_model(repo_id)
                return success, message
            except Exception as e:
                return False, f"Download error: {str(e)}"

        # For now, we'll do a synchronous download (can be made async later)
        success, message = download_model()

        if success:
            return jsonify({
                "success": True,
                "message": f"Successfully downloaded {repo_id}",
                "repo_id": repo_id
            })
        else:
            return jsonify({"success": False, "error": message})

    except Exception as e:
        return jsonify({"success": False, "error": f"Server error: {str(e)}"})

def cleanup():
    """Clean up resources"""
    global cap
    if cap is not None:
        cap.release()
        print("📹 Camera released")

if __name__ == "__main__":

    print("🚀 Starting YOLO Detection Web App with In-Process Model Switching")
    print("=" * 70)

    # Initialize GPU detection
    print("\n🔍 Detecting system capabilities...")
    gpu_info = detect_gpu_info()
    if gpu_info["torch_cuda_available"]:
        print(f"✅ GPU detected: {gpu_info['device_name']}")
        print(f"   Memory: {gpu_info['gpu_memory_total']} GB total")
        print(f"   CUDA devices: {gpu_info['cuda_device_count']}")
    else:
        print("⚠️  No GPU detected - using CPU for inference")
        print("   Consider installing CUDA-enabled PyTorch for better performance")

    # Show available models
    models = find_yolo_models()
    print(f"📋 Found {len(models)} YOLO models:")
    for i, model_info in enumerate(models[:10]):  # Show first 10
        status = "🟢 Local" if model_info["type"] == "local" else "🔵 Remote"
        print(f"   {i+1}. {model_info['name']} ({model_info['size_mb']}{'MB' if model_info['type'] == 'local' else ''}) {status}")
    if len(models) > 10:
        print(f"   ... and {len(models) - 10} more")

    print(f"\n🎯 Current model: {current_model_path}")

    # Initialize components
    if not initialize_model():
        print("❌ Failed to initialize model. Exiting.")
        sys.exit(1)

    if not initialize_camera():
        print("❌ Failed to initialize camera. Exiting.")
        sys.exit(1)

    try:
        # Use waitress for production-grade serving
        from waitress import serve
        print("\n🌐 Server starting...")
        print("📱 Open your browser and go to: http://localhost:8000")
        print("🔄 Use the dropdown menu to switch between models instantly")
        print("⚡ No server restart needed - models switch in real-time!")
        print("🛑 Press Ctrl+C to stop the server")
        print("=" * 70)

        serve(app, host="0.0.0.0", port=8000, threads=4)

    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")
    finally:
        cleanup()
