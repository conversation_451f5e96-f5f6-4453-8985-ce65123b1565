@echo off
echo 🚀 YOLO Detection Web App - Auto-Restart Wrapper
echo ============================================================
echo 📝 This script handles automatic restarts for model switching
echo 🔄 When you switch models, the app will restart automatically  
echo 🛑 Press Ctrl+C to stop completely
echo ============================================================

set restart_count=0
set max_restarts=10

:restart_loop
if %restart_count% geq %max_restarts% (
    echo ❌ Maximum restart attempts reached
    goto end
)

echo.
echo 🔄 Starting application (attempt %restart_count%/10)
python app.py

set exit_code=%ERRORLEVEL%

if %exit_code%==0 (
    echo ✅ Application stopped normally
    goto end
) else if %exit_code%==42 (
    echo 🔄 Model switch requested - restarting...
    set /a restart_count+=1
    timeout /t 1 /nobreak >nul
    goto restart_loop
) else (
    echo ❌ Application exited with error code: %exit_code%
    if %restart_count% lss 3 (
        echo 🔄 Attempting restart...
        set /a restart_count+=1
        timeout /t 2 /nobreak >nul
        goto restart_loop
    ) else (
        echo ❌ Too many errors, stopping
        goto end
    )
)

:end
echo.
echo 👋 Goodbye!
pause
