#!/usr/bin/env python3
"""
Test script for model switching functionality
"""

import requests
import json
import time

def test_model_switching():
    """Test the model switching API"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Model Switching Functionality")
    print("=" * 50)
    
    # Test 1: Get available models
    print("1. Testing /models endpoint...")
    try:
        response = requests.get(f"{base_url}/models")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Found {len(data['models'])} models")
            print(f"   📍 Current model: {data['current_model']}")
            
            # Show first few models
            for i, model in enumerate(data['models'][:5]):
                status = "🟢" if model['type'] == 'local' else "🔵"
                print(f"      {i+1}. {model['name']} {status}")
            
            return data['models']
        else:
            print(f"   ❌ Failed: {response.status_code}")
            return []
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return []

def test_status_endpoint():
    """Test the status endpoint"""
    print("\n2. Testing /status endpoint...")
    try:
        response = requests.get("http://localhost:8000/status")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Model loaded: {data['model_loaded']}")
            print(f"   📹 Camera available: {data['camera_available']}")
            print(f"   🎯 Current model: {data['current_model']}")
            return True
        else:
            print(f"   ❌ Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def test_model_switch(target_model):
    """Test switching to a specific model"""
    print(f"\n3. Testing model switch to: {target_model}")
    try:
        response = requests.post(
            "http://localhost:8000/switch_model",
            headers={"Content-Type": "application/json"},
            json={"model_path": target_model}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"   ✅ Switch request successful: {data['message']}")
                print("   ⏳ Server should restart automatically...")
                return True
            else:
                print(f"   ❌ Switch failed: {data.get('error', 'Unknown error')}")
                return False
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

if __name__ == "__main__":
    # Test basic functionality
    models = test_model_switching()
    
    if not models:
        print("❌ Cannot proceed without model list")
        exit(1)
    
    # Test status
    if not test_status_endpoint():
        print("❌ Status endpoint failed")
        exit(1)
    
    # Find a different model to test switching
    current_model = None
    try:
        response = requests.get("http://localhost:8000/models")
        current_model = response.json()['current_model']
    except:
        pass
    
    # Find a good test model (prefer a small one)
    test_models = [
        "yolo11n.pt",      # Small detection model
        "yolo11s.pt",      # Small-medium detection model
        "yolo11n-seg.pt",  # Small segmentation model
    ]
    
    target_model = None
    for test_model in test_models:
        if test_model != current_model and any(m['name'] == test_model for m in models):
            target_model = test_model
            break
    
    if target_model:
        print(f"\n🎯 Will test switching from {current_model} to {target_model}")
        print("⚠️  Note: This will restart the server!")
        
        # Uncomment the line below to actually test model switching
        # test_model_switch(target_model)
        print("   (Test disabled - uncomment line in script to enable)")
    else:
        print("\n⚠️  No suitable test model found for switching")
    
    print("\n" + "=" * 50)
    print("✅ Model switching functionality is ready!")
    print("\n📝 How to use:")
    print("1. Open http://localhost:8000 in your browser")
    print("2. Use the dropdown menu to select a different model")
    print("3. Click 'Switch Model' button")
    print("4. Wait for the server to restart automatically")
    print("5. The page will reload with the new model")
