#!/usr/bin/env python3
"""
Quick script to download YOLOS models from Hugging Face
"""

import os
import sys
import subprocess

def install_huggingface_hub():
    """Install huggingface_hub if not available"""
    try:
        import huggingface_hub
        return True
    except ImportError:
        print("📦 Installing huggingface_hub...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "huggingface_hub"], 
                          check=True, capture_output=True)
            print("✅ huggingface_hub installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install huggingface_hub: {e}")
            return False

def download_yolos_model(model_size="small"):
    """Download YOLOS model from Hugging Face"""
    if not install_huggingface_hub():
        return False
    
    try:
        from huggingface_hub import hf_hub_download
        
        repo_id = f"hustvl/yolos-{model_size}"
        print(f"🤗 Downloading YOLOS-{model_size} from {repo_id}")
        
        # Download the PyTorch model file
        try:
            local_path = hf_hub_download(
                repo_id=repo_id,
                filename="pytorch_model.bin",
                local_dir=f"./yolos-{model_size}",
                local_dir_use_symlinks=False
            )
            print(f"✅ Downloaded pytorch_model.bin to ./yolos-{model_size}/")
        except Exception as e:
            print(f"⚠️  pytorch_model.bin not found, trying config.json: {e}")
        
        # Download config file
        try:
            config_path = hf_hub_download(
                repo_id=repo_id,
                filename="config.json",
                local_dir=f"./yolos-{model_size}",
                local_dir_use_symlinks=False
            )
            print(f"✅ Downloaded config.json to ./yolos-{model_size}/")
        except Exception as e:
            print(f"⚠️  config.json not found: {e}")
        
        # Try to download other common files
        common_files = ["model.safetensors", "preprocessor_config.json", "README.md"]
        for filename in common_files:
            try:
                file_path = hf_hub_download(
                    repo_id=repo_id,
                    filename=filename,
                    local_dir=f"./yolos-{model_size}",
                    local_dir_use_symlinks=False
                )
                print(f"✅ Downloaded {filename}")
            except:
                pass  # File might not exist
        
        print(f"\n🎉 YOLOS-{model_size} download completed!")
        print(f"📁 Files saved to: ./yolos-{model_size}/")
        
        return True
        
    except Exception as e:
        print(f"❌ Error downloading YOLOS model: {e}")
        return False

def main():
    """Main function"""
    print("🚀 YOLOS Model Downloader")
    print("=" * 40)
    
    print("\n🎯 Available YOLOS models:")
    print("1. yolos-tiny")
    print("2. yolos-small") 
    print("3. yolos-base")
    
    try:
        choice = input("\nEnter your choice (1-3) or model size directly: ").strip()
        
        if choice == "1":
            model_size = "tiny"
        elif choice == "2":
            model_size = "small"
        elif choice == "3":
            model_size = "base"
        elif choice in ["tiny", "small", "base"]:
            model_size = choice
        else:
            print("❌ Invalid choice. Using 'small' as default.")
            model_size = "small"
        
        success = download_yolos_model(model_size)
        
        if success:
            print(f"\n📝 Next steps:")
            print(f"1. The YOLOS model is downloaded but needs conversion to work with Ultralytics")
            print(f"2. YOLOS uses a different format than YOLO models")
            print(f"3. You may need additional conversion scripts")
            print(f"4. For immediate use, try downloading Ultralytics YOLO models instead")
        
    except KeyboardInterrupt:
        print("\n👋 Cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
