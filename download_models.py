#!/usr/bin/env python3
"""
Model Download Utility for YOLO Detection Web App
Supports downloading models from various sources including Hugging Face
"""

import os
import sys
import requests
import subprocess
from pathlib import Path
from urllib.parse import urlparse
import json

def install_dependencies():
    """Install required dependencies for model downloading"""
    print("📦 Installing additional dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", 
                       "huggingface-hub", "transformers", "timm"], 
                      check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def download_from_url(url, filename=None):
    """Download a model from a direct URL"""
    try:
        if not filename:
            filename = os.path.basename(urlparse(url).path)
        
        print(f"📥 Downloading {filename} from {url}")
        
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\r   Progress: {percent:.1f}% ({downloaded}/{total_size} bytes)", end="")
        
        print(f"\n✅ Downloaded {filename} successfully")
        return True
        
    except Exception as e:
        print(f"\n❌ Error downloading {filename}: {e}")
        return False

def download_from_huggingface(repo_id, filename=None, subfolder=None):
    """Download a model from Hugging Face Hub"""
    try:
        # Try to import huggingface_hub
        try:
            from huggingface_hub import hf_hub_download, list_repo_files
        except ImportError:
            print("❌ huggingface_hub not installed. Installing...")
            if not install_dependencies():
                return False
            from huggingface_hub import hf_hub_download, list_repo_files
        
        print(f"🤗 Downloading from Hugging Face: {repo_id}")
        
        # List available files if no filename specified
        if not filename:
            print("📋 Available files in repository:")
            files = list_repo_files(repo_id)
            model_files = [f for f in files if f.endswith(('.pt', '.pth', '.onnx', '.engine', '.bin', '.safetensors'))]
            
            if not model_files:
                print("❌ No model files found in repository")
                return False
            
            for i, file in enumerate(model_files):
                print(f"   {i+1}. {file}")
            
            if len(model_files) == 1:
                filename = model_files[0]
                print(f"🎯 Auto-selecting: {filename}")
            else:
                try:
                    choice = int(input("Enter file number to download: ")) - 1
                    filename = model_files[choice]
                except (ValueError, IndexError):
                    print("❌ Invalid selection")
                    return False
        
        # Download the file
        local_path = hf_hub_download(
            repo_id=repo_id,
            filename=filename,
            subfolder=subfolder,
            local_dir=".",
            local_dir_use_symlinks=False
        )
        
        print(f"✅ Downloaded {filename} from {repo_id}")
        return True
        
    except Exception as e:
        print(f"❌ Error downloading from Hugging Face: {e}")
        return False

def download_ultralytics_model(model_name):
    """Download a model using Ultralytics (will auto-download)"""
    try:
        from ultralytics import YOLO
        print(f"📥 Downloading Ultralytics model: {model_name}")
        
        # This will trigger download if not present
        model = YOLO(model_name)
        print(f"✅ Downloaded {model_name} successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error downloading {model_name}: {e}")
        return False

def list_popular_models():
    """List popular models available for download"""
    models = {
        "🔥 Ultralytics YOLO Models": [
            "yolo11n.pt", "yolo11s.pt", "yolo11m.pt", "yolo11l.pt", "yolo11x.pt",
            "yolo11n-seg.pt", "yolo11s-seg.pt", "yolo11m-seg.pt", "yolo11l-seg.pt", "yolo11x-seg.pt",
            "yolo11n-pose.pt", "yolo11s-pose.pt", "yolo11m-pose.pt", "yolo11l-pose.pt", "yolo11x-pose.pt",
            "yolov8n.pt", "yolov8s.pt", "yolov8m.pt", "yolov8l.pt", "yolov8x.pt",
            "yolov10n.pt", "yolov10s.pt", "yolov10m.pt", "yolov10l.pt", "yolov10x.pt"
        ],
        "🤗 Hugging Face Models": [
            "hustvl/yolos-small",
            "hustvl/yolos-base", 
            "hustvl/yolos-tiny",
            "microsoft/yolov5",
            "keremberke/yolov8n-table-extraction",
            "keremberke/yolov8s-table-extraction",
            "arnabdhar/YOLOv8-Face-Detection"
        ],
        "🌐 Direct Download URLs": [
            "Custom models from direct URLs"
        ]
    }
    
    print("\n📋 Popular Models Available for Download:")
    print("=" * 60)
    
    for category, model_list in models.items():
        print(f"\n{category}")
        for model in model_list:
            print(f"   • {model}")

def main():
    """Main function for interactive model downloading"""
    print("🚀 YOLO Model Download Utility")
    print("=" * 50)
    
    while True:
        print("\n🎯 What would you like to do?")
        print("1. 📋 List popular models")
        print("2. 🤗 Download from Hugging Face")
        print("3. ⚡ Download Ultralytics model")
        print("4. 🌐 Download from URL")
        print("5. 📁 Show current models")
        print("6. ❌ Exit")
        
        try:
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == "1":
                list_popular_models()
                
            elif choice == "2":
                repo_id = input("Enter Hugging Face repo ID (e.g., hustvl/yolos-small): ").strip()
                if repo_id:
                    filename = input("Enter filename (or press Enter to see available files): ").strip()
                    download_from_huggingface(repo_id, filename if filename else None)
                
            elif choice == "3":
                model_name = input("Enter Ultralytics model name (e.g., yolo11n.pt): ").strip()
                if model_name:
                    download_ultralytics_model(model_name)
                
            elif choice == "4":
                url = input("Enter direct download URL: ").strip()
                if url:
                    filename = input("Enter filename (or press Enter for auto): ").strip()
                    download_from_url(url, filename if filename else None)
                
            elif choice == "5":
                print("\n📁 Current models in directory:")
                model_files = list(Path(".").glob("*.pt")) + list(Path(".").glob("*.onnx")) + list(Path(".").glob("*.engine"))
                if model_files:
                    for i, model_file in enumerate(model_files, 1):
                        size_mb = model_file.stat().st_size / (1024 * 1024)
                        print(f"   {i}. {model_file.name} ({size_mb:.1f} MB)")
                else:
                    print("   No model files found")
                
            elif choice == "6":
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice. Please enter 1-6.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
