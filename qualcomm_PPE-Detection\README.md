---
library_name: pytorch
license: other
tags:
- real_time
- android
pipeline_tag: object-detection

---

![](https://qaihub-public-assets.s3.us-west-2.amazonaws.com/qai-hub-models/models/gear_guard_net/web-assets/model_demo.png)

# PPE-Detection: Optimized for Mobile Deployment
## Object detection for personal protective equipments (PPE)

Detect if a person is wearing personal protective equipments (PPE) in real-time.  This model's architecture was developed by Qualcomm. The model was trained by Qualcomm on a proprietary dataset, but can be used on any image.

This repository provides scripts to run PPE-Detection on Qualcomm® devices.
More details on model performance across various devices, can be found
[here](https://aihub.qualcomm.com/models/gear_guard_net).


### Model Details

- **Model Type:** Model_use_case.object_detection
- **Model Stats:**
  - Inference latency: RealTime
  - Input resolution: 320x192
  - Number of output classes: 2
  - Number of parameters: 6.19M
  - Model size (float): 23.6 MB
  - Model size (w8a8): 6.23 MB
  - Model size (w8a16): 6.65 MB

| Model | Precision | Device | Chipset | Target Runtime | Inference Time (ms) | Peak Memory Range (MB) | Primary Compute Unit | Target Model
|---|---|---|---|---|---|---|---|---|
| PPE-Detection | float | QCS8275 (Proxy) | Qualcomm® QCS8275 (Proxy) | TFLITE | 4.148 ms | 0 - 19 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | QCS8275 (Proxy) | Qualcomm® QCS8275 (Proxy) | QNN_DLC | 4.036 ms | 0 - 15 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | QCS8450 (Proxy) | Qualcomm® QCS8450 (Proxy) | TFLITE | 1.401 ms | 0 - 35 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | QCS8450 (Proxy) | Qualcomm® QCS8450 (Proxy) | QNN_DLC | 1.566 ms | 1 - 25 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | QCS8550 (Proxy) | Qualcomm® QCS8550 (Proxy) | TFLITE | 0.667 ms | 0 - 135 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | QCS8550 (Proxy) | Qualcomm® QCS8550 (Proxy) | QNN_DLC | 0.656 ms | 1 - 74 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | QCS9075 (Proxy) | Qualcomm® QCS9075 (Proxy) | TFLITE | 1.273 ms | 0 - 20 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | QCS9075 (Proxy) | Qualcomm® QCS9075 (Proxy) | QNN_DLC | 1.238 ms | 0 - 16 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | SA7255P ADP | Qualcomm® SA7255P | TFLITE | 4.148 ms | 0 - 19 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | SA7255P ADP | Qualcomm® SA7255P | QNN_DLC | 4.036 ms | 0 - 15 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | SA8255 (Proxy) | Qualcomm® SA8255P (Proxy) | TFLITE | 0.669 ms | 0 - 134 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | SA8255 (Proxy) | Qualcomm® SA8255P (Proxy) | QNN_DLC | 0.664 ms | 1 - 73 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | SA8295P ADP | Qualcomm® SA8295P | TFLITE | 1.81 ms | 0 - 19 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | SA8295P ADP | Qualcomm® SA8295P | QNN_DLC | 1.764 ms | 0 - 18 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | SA8650 (Proxy) | Qualcomm® SA8650P (Proxy) | TFLITE | 0.659 ms | 0 - 134 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | SA8650 (Proxy) | Qualcomm® SA8650P (Proxy) | QNN_DLC | 0.664 ms | 1 - 73 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | SA8775P ADP | Qualcomm® SA8775P | TFLITE | 1.273 ms | 0 - 20 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | SA8775P ADP | Qualcomm® SA8775P | QNN_DLC | 1.238 ms | 0 - 16 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | Samsung Galaxy S23 | Snapdragon® 8 Gen 2 Mobile | TFLITE | 0.665 ms | 0 - 134 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | Samsung Galaxy S23 | Snapdragon® 8 Gen 2 Mobile | QNN_DLC | 0.663 ms | 0 - 72 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | Samsung Galaxy S23 | Snapdragon® 8 Gen 2 Mobile | ONNX | 0.915 ms | 0 - 60 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.onnx) |
| PPE-Detection | float | Samsung Galaxy S24 | Snapdragon® 8 Gen 3 Mobile | TFLITE | 0.485 ms | 0 - 34 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | Samsung Galaxy S24 | Snapdragon® 8 Gen 3 Mobile | QNN_DLC | 0.471 ms | 1 - 28 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | Samsung Galaxy S24 | Snapdragon® 8 Gen 3 Mobile | ONNX | 0.643 ms | 0 - 30 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.onnx) |
| PPE-Detection | float | Snapdragon 8 Elite QRD | Snapdragon® 8 Elite Mobile | TFLITE | 0.481 ms | 0 - 24 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.tflite) |
| PPE-Detection | float | Snapdragon 8 Elite QRD | Snapdragon® 8 Elite Mobile | QNN_DLC | 0.44 ms | 1 - 23 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | Snapdragon 8 Elite QRD | Snapdragon® 8 Elite Mobile | ONNX | 0.643 ms | 1 - 22 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.onnx) |
| PPE-Detection | float | Snapdragon X Elite CRD | Snapdragon® X Elite | QNN_DLC | 0.768 ms | 55 - 55 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.dlc) |
| PPE-Detection | float | Snapdragon X Elite CRD | Snapdragon® X Elite | ONNX | 0.967 ms | 13 - 13 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection.onnx) |
| PPE-Detection | w8a16 | QCS8275 (Proxy) | Qualcomm® QCS8275 (Proxy) | QNN_DLC | 1.449 ms | 0 - 16 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | QCS8450 (Proxy) | Qualcomm® QCS8450 (Proxy) | QNN_DLC | 0.7 ms | 0 - 29 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | QCS8550 (Proxy) | Qualcomm® QCS8550 (Proxy) | QNN_DLC | 0.471 ms | 0 - 48 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | QCS9075 (Proxy) | Qualcomm® QCS9075 (Proxy) | QNN_DLC | 0.666 ms | 0 - 16 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | RB3 Gen 2 (Proxy) | Qualcomm® QCS6490 (Proxy) | QNN_DLC | 2.918 ms | 0 - 26 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | SA7255P ADP | Qualcomm® SA7255P | QNN_DLC | 1.449 ms | 0 - 16 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | SA8255 (Proxy) | Qualcomm® SA8255P (Proxy) | QNN_DLC | 0.468 ms | 0 - 5 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | SA8295P ADP | Qualcomm® SA8295P | QNN_DLC | 1.064 ms | 0 - 18 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | SA8650 (Proxy) | Qualcomm® SA8650P (Proxy) | QNN_DLC | 0.471 ms | 0 - 49 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | SA8775P ADP | Qualcomm® SA8775P | QNN_DLC | 0.666 ms | 0 - 16 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | Samsung Galaxy S23 | Snapdragon® 8 Gen 2 Mobile | QNN_DLC | 0.474 ms | 0 - 48 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | Samsung Galaxy S23 | Snapdragon® 8 Gen 2 Mobile | ONNX | 0.674 ms | 0 - 59 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.onnx) |
| PPE-Detection | w8a16 | Samsung Galaxy S24 | Snapdragon® 8 Gen 3 Mobile | QNN_DLC | 0.316 ms | 0 - 39 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | Samsung Galaxy S24 | Snapdragon® 8 Gen 3 Mobile | ONNX | 0.433 ms | 0 - 35 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.onnx) |
| PPE-Detection | w8a16 | Snapdragon 8 Elite QRD | Snapdragon® 8 Elite Mobile | QNN_DLC | 0.259 ms | 0 - 22 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | Snapdragon 8 Elite QRD | Snapdragon® 8 Elite Mobile | ONNX | 0.539 ms | 0 - 23 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.onnx) |
| PPE-Detection | w8a16 | Snapdragon X Elite CRD | Snapdragon® X Elite | QNN_DLC | 0.612 ms | 45 - 45 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.dlc) |
| PPE-Detection | w8a16 | Snapdragon X Elite CRD | Snapdragon® X Elite | ONNX | 0.731 ms | 6 - 6 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a16.onnx) |
| PPE-Detection | w8a8 | QCS8275 (Proxy) | Qualcomm® QCS8275 (Proxy) | TFLITE | 0.865 ms | 0 - 15 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | QCS8275 (Proxy) | Qualcomm® QCS8275 (Proxy) | QNN_DLC | 0.825 ms | 0 - 16 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | QCS8450 (Proxy) | Qualcomm® QCS8450 (Proxy) | TFLITE | 0.353 ms | 0 - 30 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | QCS8450 (Proxy) | Qualcomm® QCS8450 (Proxy) | QNN_DLC | 0.487 ms | 0 - 33 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | QCS8550 (Proxy) | Qualcomm® QCS8550 (Proxy) | TFLITE | 0.247 ms | 0 - 50 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | QCS8550 (Proxy) | Qualcomm® QCS8550 (Proxy) | QNN_DLC | 0.241 ms | 0 - 51 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | QCS9075 (Proxy) | Qualcomm® QCS9075 (Proxy) | TFLITE | 0.44 ms | 0 - 17 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | QCS9075 (Proxy) | Qualcomm® QCS9075 (Proxy) | QNN_DLC | 0.435 ms | 0 - 17 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | RB3 Gen 2 (Proxy) | Qualcomm® QCS6490 (Proxy) | TFLITE | 1.4 ms | 0 - 25 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | RB3 Gen 2 (Proxy) | Qualcomm® QCS6490 (Proxy) | QNN_DLC | 1.8 ms | 0 - 25 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | RB5 (Proxy) | Qualcomm® QCS8250 (Proxy) | TFLITE | 4.664 ms | 0 - 3 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | SA7255P ADP | Qualcomm® SA7255P | TFLITE | 0.865 ms | 0 - 15 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | SA7255P ADP | Qualcomm® SA7255P | QNN_DLC | 0.825 ms | 0 - 16 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | SA8255 (Proxy) | Qualcomm® SA8255P (Proxy) | TFLITE | 0.234 ms | 0 - 51 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | SA8255 (Proxy) | Qualcomm® SA8255P (Proxy) | QNN_DLC | 0.23 ms | 0 - 51 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | SA8295P ADP | Qualcomm® SA8295P | TFLITE | 0.697 ms | 0 - 19 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | SA8295P ADP | Qualcomm® SA8295P | QNN_DLC | 0.686 ms | 0 - 19 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | SA8650 (Proxy) | Qualcomm® SA8650P (Proxy) | TFLITE | 0.24 ms | 0 - 50 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | SA8650 (Proxy) | Qualcomm® SA8650P (Proxy) | QNN_DLC | 0.234 ms | 0 - 52 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | SA8775P ADP | Qualcomm® SA8775P | TFLITE | 0.44 ms | 0 - 17 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | SA8775P ADP | Qualcomm® SA8775P | QNN_DLC | 0.435 ms | 0 - 17 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | Samsung Galaxy S23 | Snapdragon® 8 Gen 2 Mobile | TFLITE | 0.243 ms | 0 - 49 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | Samsung Galaxy S23 | Snapdragon® 8 Gen 2 Mobile | QNN_DLC | 0.234 ms | 0 - 6 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | Samsung Galaxy S23 | Snapdragon® 8 Gen 2 Mobile | ONNX | 0.358 ms | 0 - 49 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.onnx) |
| PPE-Detection | w8a8 | Samsung Galaxy S24 | Snapdragon® 8 Gen 3 Mobile | TFLITE | 0.182 ms | 0 - 38 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | Samsung Galaxy S24 | Snapdragon® 8 Gen 3 Mobile | QNN_DLC | 0.175 ms | 0 - 39 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | Samsung Galaxy S24 | Snapdragon® 8 Gen 3 Mobile | ONNX | 0.29 ms | 0 - 34 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.onnx) |
| PPE-Detection | w8a8 | Snapdragon 8 Elite QRD | Snapdragon® 8 Elite Mobile | TFLITE | 0.202 ms | 0 - 17 MB | NPU | [PPE-Detection.tflite](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.tflite) |
| PPE-Detection | w8a8 | Snapdragon 8 Elite QRD | Snapdragon® 8 Elite Mobile | QNN_DLC | 0.187 ms | 0 - 23 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | Snapdragon 8 Elite QRD | Snapdragon® 8 Elite Mobile | ONNX | 0.28 ms | 0 - 21 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.onnx) |
| PPE-Detection | w8a8 | Snapdragon X Elite CRD | Snapdragon® X Elite | QNN_DLC | 0.352 ms | 40 - 40 MB | NPU | [PPE-Detection.dlc](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.dlc) |
| PPE-Detection | w8a8 | Snapdragon X Elite CRD | Snapdragon® X Elite | ONNX | 0.392 ms | 6 - 6 MB | NPU | [PPE-Detection.onnx](https://huggingface.co/qualcomm/PPE-Detection/blob/main/PPE-Detection_w8a8.onnx) |




## Installation


Install the package via pip:
```bash
pip install qai-hub-models
```


## Configure Qualcomm® AI Hub to run this model on a cloud-hosted device

Sign-in to [Qualcomm® AI Hub](https://app.aihub.qualcomm.com/) with your
Qualcomm® ID. Once signed in navigate to `Account -> Settings -> API Token`.

With this API token, you can configure your client to run models on the cloud
hosted devices.
```bash
qai-hub configure --api_token API_TOKEN
```
Navigate to [docs](https://app.aihub.qualcomm.com/docs/) for more information.



## Demo off target

The package contains a simple end-to-end demo that downloads pre-trained
weights and runs this model on a sample input.

```bash
python -m qai_hub_models.models.gear_guard_net.demo
```

The above demo runs a reference implementation of pre-processing, model
inference, and post processing.

**NOTE**: If you want running in a Jupyter Notebook or Google Colab like
environment, please add the following to your cell (instead of the above).
```
%run -m qai_hub_models.models.gear_guard_net.demo
```


### Run model on a cloud-hosted device

In addition to the demo, you can also run the model on a cloud-hosted Qualcomm®
device. This script does the following:
* Performance check on-device on a cloud-hosted device
* Downloads compiled assets that can be deployed on-device for Android.
* Accuracy check between PyTorch and on-device outputs.

```bash
python -m qai_hub_models.models.gear_guard_net.export
```
```
Profiling Results
------------------------------------------------------------
PPE-Detection
Device                          : cs_8275 (ANDROID 14)                
Runtime                         : TFLITE                              
Estimated inference time (ms)   : 4.1                                 
Estimated peak memory usage (MB): [0, 19]                             
Total # Ops                     : 77                                  
Compute Unit(s)                 : npu (77 ops) gpu (0 ops) cpu (0 ops)
```


## How does this work?

This [export script](https://aihub.qualcomm.com/models/gear_guard_net/qai_hub_models/models/PPE-Detection/export.py)
leverages [Qualcomm® AI Hub](https://aihub.qualcomm.com/) to optimize, validate, and deploy this model
on-device. Lets go through each step below in detail:

Step 1: **Compile model for on-device deployment**

To compile a PyTorch model for on-device deployment, we first trace the model
in memory using the `jit.trace` and then call the `submit_compile_job` API.

```python
import torch

import qai_hub as hub
from qai_hub_models.models.gear_guard_net import Model

# Load the model
torch_model = Model.from_pretrained()

# Device
device = hub.Device("Samsung Galaxy S24")

# Trace model
input_shape = torch_model.get_input_spec()
sample_inputs = torch_model.sample_inputs()

pt_model = torch.jit.trace(torch_model, [torch.tensor(data[0]) for _, data in sample_inputs.items()])

# Compile model on a specific device
compile_job = hub.submit_compile_job(
    model=pt_model,
    device=device,
    input_specs=torch_model.get_input_spec(),
)

# Get target model to run on-device
target_model = compile_job.get_target_model()

```


Step 2: **Performance profiling on cloud-hosted device**

After compiling models from step 1. Models can be profiled model on-device using the
`target_model`. Note that this scripts runs the model on a device automatically
provisioned in the cloud.  Once the job is submitted, you can navigate to a
provided job URL to view a variety of on-device performance metrics.
```python
profile_job = hub.submit_profile_job(
    model=target_model,
    device=device,
)
        
```

Step 3: **Verify on-device accuracy**

To verify the accuracy of the model on-device, you can run on-device inference
on sample input data on the same cloud hosted device.
```python
input_data = torch_model.sample_inputs()
inference_job = hub.submit_inference_job(
    model=target_model,
    device=device,
    inputs=input_data,
)
    on_device_output = inference_job.download_output_data()

```
With the output of the model, you can compute like PSNR, relative errors or
spot check the output with expected output.

**Note**: This on-device profiling and inference requires access to Qualcomm®
AI Hub. [Sign up for access](https://myaccount.qualcomm.com/signup).



## Run demo on a cloud-hosted device

You can also run the demo on-device.

```bash
python -m qai_hub_models.models.gear_guard_net.demo --eval-mode on-device
```

**NOTE**: If you want running in a Jupyter Notebook or Google Colab like
environment, please add the following to your cell (instead of the above).
```
%run -m qai_hub_models.models.gear_guard_net.demo -- --eval-mode on-device
```


## Deploying compiled model to Android


The models can be deployed using multiple runtimes:
- TensorFlow Lite (`.tflite` export): [This
  tutorial](https://www.tensorflow.org/lite/android/quickstart) provides a
  guide to deploy the .tflite model in an Android application.


- QNN (`.so` export ): This [sample
  app](https://docs.qualcomm.com/bundle/publicresource/topics/80-63442-50/sample_app.html)
provides instructions on how to use the `.so` shared library  in an Android application.


## View on Qualcomm® AI Hub
Get more details on PPE-Detection's performance across various devices [here](https://aihub.qualcomm.com/models/gear_guard_net).
Explore all available models on [Qualcomm® AI Hub](https://aihub.qualcomm.com/)


## License
* The license for the original implementation of PPE-Detection can be found
  [here](https://github.com/quic/ai-hub-models/blob/main/LICENSE).
* The license for the compiled assets for on-device deployment can be found [here](https://qaihub-public-assets.s3.us-west-2.amazonaws.com/qai-hub-models/Qualcomm+AI+Hub+Proprietary+License.pdf)




## Community
* Join [our AI Hub Slack community](https://aihub.qualcomm.com/community/slack) to collaborate, post questions and learn more about on-device AI.
* For questions or feedback please [reach out to us](mailto:<EMAIL>).


