# 📥 Model Download Guide

## 🚀 Quick Start

### Method 1: Interactive Download Tool
```bash
python download_models.py
```
*Follow the interactive menu to download models*

### Method 2: Download Specific YOLOS Model
```bash
python download_yolos.py
```
*Downloads YOLOS models from Hugging Face*

### Method 3: Manual Download
*Place model files directly in the project folder*

---

## 🎯 Supported Model Sources

### 1. 🔥 **Ultralytics Models** (Recommended)
**Auto-download when selected in the app**

Popular models:
- `yolo11n.pt` - Nano (fastest)
- `yolo11s.pt` - Small 
- `yolo11m.pt` - Medium
- `yolo11l.pt` - Large
- `yolo11x.pt` - Extra Large

**Segmentation models:**
- `yolo11n-seg.pt`, `yolo11s-seg.pt`, etc.

**Pose detection:**
- `yolo11n-pose.pt`, `yolo11s-pose.pt`, etc.

### 2. 🤗 **Hugging Face Models**

**YOLOS (You Only Look Once with Transformers):**
- `hustvl/yolos-tiny`
- `hustvl/yolos-small` ← *The one you mentioned*
- `hustvl/yolos-base`

**Other popular models:**
- `microsoft/yolov5`
- `keremberke/yolov8n-table-extraction`
- `arnabdhar/YOLOv8-Face-Detection`

### 3. 🌐 **Direct URLs**
Download from any direct link to `.pt`, `.onnx`, or `.engine` files

---

## 📋 Step-by-Step: Download YOLOS-Small

### Option A: Using the Download Script
```bash
python download_yolos.py
# Select option 2 for "small"
```

### Option B: Manual Hugging Face Download
```bash
# Install huggingface_hub first
pip install huggingface_hub

# Download using Python
python -c "
from huggingface_hub import hf_hub_download
hf_hub_download('hustvl/yolos-small', 'pytorch_model.bin', local_dir='./yolos-small')
hf_hub_download('hustvl/yolos-small', 'config.json', local_dir='./yolos-small')
"
```

### Option C: Using Git LFS
```bash
git lfs install
git clone https://huggingface.co/hustvl/yolos-small
```

---

## 🔧 Using Downloaded Models

### ✅ **Ultralytics Models**
- **Auto-detected** - appear in dropdown immediately
- **Ready to use** - no conversion needed

### ⚠️ **Hugging Face Models (YOLOS)**
- **May need conversion** to work with Ultralytics
- **Different format** than standard YOLO
- **Experimental support** - might not work directly

### 🛠️ **Custom Models**
1. **Place in project folder** or `./custom_models/`
2. **Restart the app** to detect new models
3. **Select from dropdown** and test

---

## 📁 Folder Structure

```
Yoloperson/
├── app.py
├── yolo11n-seg.pt          # ✅ Works
├── yolo11n.pt              # ✅ Works  
├── custom_model.pt         # ✅ Should work
├── yolos-small/            # ⚠️ May need conversion
│   ├── pytorch_model.bin
│   └── config.json
└── custom_models/          # ✅ Custom folder
    └── my_model.pt
```

---

## 🧪 Testing New Models

1. **Download model** using any method above
2. **Restart app** (if needed): `python app.py`
3. **Check dropdown** - new models should appear
4. **Select and test** - switch to the new model
5. **Verify detection** - check if it works with your webcam

---

## 🛠️ Troubleshooting

### Model Not Appearing in Dropdown
- **Check file extension** - must be `.pt`, `.onnx`, `.engine`, or `.bin`
- **Check file size** - must be > 1MB
- **Restart app** - refresh model detection
- **Check location** - place in main folder or supported subfolders

### Model Fails to Load
- **Check format** - Ultralytics models work best
- **Check compatibility** - some HF models need conversion
- **Check file integrity** - re-download if corrupted

### YOLOS Models Not Working
- **Different architecture** - YOLOS uses transformers, not Ultralytics
- **Conversion needed** - may require additional scripts
- **Use Ultralytics instead** - for immediate compatibility

---

## 🎉 Recommended Downloads

### For Beginners:
```bash
# These auto-download when selected:
yolo11n.pt       # Fast detection
yolo11n-seg.pt   # Fast segmentation
yolo11s.pt       # Better accuracy
```

### For Advanced Users:
```bash
python download_models.py
# Try various Hugging Face models
# Experiment with custom models
```

### For YOLOS Specifically:
```bash
python download_yolos.py
# Download YOLOS-small as requested
# Note: May need additional setup
```

---

**🚀 Start with Ultralytics models for guaranteed compatibility, then experiment with others!**
