# 🎯 YOLO Person Segmentation Web App

A real-time web application that shows your webcam feed with **person detection and segmentation** using YOLO (You Only Look Once) deep learning model.

## ✨ Features

- **🎯 Multiple YOLO Models** - Switch between 30+ YOLO models on-the-fly
- **🔄 Live Model Switching** - Change models without stopping the app
- **🤖 Auto-Detection** - Automatically finds local and downloadable models
- **📊 Model Information** - Shows model type, size, and capabilities
- **🎭 Multiple Tasks** - Detection, segmentation, and pose estimation
- **🌐 Web-based Interface** - Works in any modern browser
- **📹 MJPEG Streaming** - Smooth real-time video playback
- **⚡ Auto-GPU Detection** - Uses CUDA if available
- **🚀 Production-ready** - Built with Waitress WSGI server

## 🚀 Quick Start

### 1. Setup (Automatic)

Run the setup script to install everything:

```bash
python setup.py
```

### 2. Run the Application

```bash
python app.py
```

### 3. Open in Browser

Go to: **http://localhost:8000**

That's it! You should see your webcam feed with real-time YOLO detection and a dropdown menu to switch between models.

## 🔄 Model Switching

The app automatically detects and lists available YOLO models:

### 🟢 Local Models
- Models already downloaded to your computer
- Shows actual file size
- Instant switching (no download required)

### 🔵 Remote Models
- 30+ pre-trained models from Ultralytics
- Auto-download when selected
- Includes detection, segmentation, and pose models

### 📝 How to Switch Models

1. **Select Model**: Use the dropdown menu in the top-right corner
2. **Click Switch**: Press the "Switch Model" button
3. **Wait**: Server restarts automatically (5-10 seconds)
4. **Reload**: Page refreshes with the new model

### 🎯 Model Types

- **Detection** (`yolo11n.pt`, `yolo11s.pt`, etc.) - Bounding boxes only
- **Segmentation** (`yolo11n-seg.pt`, `yolo11s-seg.pt`, etc.) - Boxes + colored masks
- **Pose** (`yolo11n-pose.pt`, `yolo11s-pose.pt`, etc.) - Human pose keypoints

### 📊 Model Sizes

- **Nano (n)** - Fastest, smallest (~6MB)
- **Small (s)** - Good balance (~22MB)
- **Medium (m)** - Better accuracy (~50MB)
- **Large (l)** - High accuracy (~100MB)
- **Extra Large (x)** - Best accuracy (~200MB+)

## 📋 Manual Setup

If you prefer manual setup:

### Prerequisites

- Python 3.8+
- Webcam connected to your computer
- (Optional) NVIDIA GPU with CUDA for faster inference

### Install Dependencies

```bash
# Create virtual environment (recommended)
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# Install packages
pip install -r requirements.txt
```

### For GPU Support (Optional)

If you have an NVIDIA GPU, install CUDA-enabled PyTorch:

```bash
# Example for CUDA 12.1 (check your CUDA version)
pip install --index-url https://download.pytorch.org/whl/cu121 torch torchvision torchaudio
```

## 🔧 Configuration

Edit `app.py` to customize settings:

```python
MODEL_PATH = "yolo11n-seg.pt"  # Model file
CAM_INDEX = 0                  # Camera index (try 1, 2 if 0 doesn't work)
CONF = 0.35                   # Confidence threshold (0.1-0.9)
IMG_SIZE = 640                # Input size (416, 512, 640, 1280)
JPEG_QUALITY = 80             # Stream quality (50-95)
```

## 🎛️ Performance Tuning

### For Higher FPS:
- Reduce `IMG_SIZE` to `416` or `512`
- Lower `JPEG_QUALITY` to `70`
- Use `yolo11n-seg.pt` (nano) instead of larger models

### For Better Accuracy:
- Increase `IMG_SIZE` to `1280`
- Use `yolo11l-seg.pt` or `yolo11x-seg.pt`
- Lower `CONF` threshold to `0.25`

## 🛠️ Troubleshooting

### No Video Stream?
1. **Check camera access**: Close other apps using the webcam
2. **Try different camera**: Change `CAM_INDEX` to `1` or `2`
3. **Check permissions**: Ensure browser/system allows camera access

### Slow Performance?
1. **Lower resolution**: Reduce `IMG_SIZE` in `app.py`
2. **Check GPU**: Run `python -c "import torch; print(torch.cuda.is_available())"`
3. **Close other apps**: Free up system resources

### Model Download Issues?
- The model downloads automatically on first run
- Check internet connection
- Manual download: The app will show download progress

## 📁 Project Structure

```
Yoloperson/
├── app.py              # Main Flask application
├── setup.py            # Automated setup script
├── requirements.txt    # Python dependencies
├── README.md          # This file
└── yolo11n-seg.pt     # YOLO model (auto-downloaded)
```

## 🔗 API Endpoints

- `GET /` - Main web interface
- `GET /video` - MJPEG video stream
- `GET /status` - Health check and status info

## 🤖 About YOLO

This app uses **YOLOv11** with segmentation capabilities:
- **Fast**: Real-time inference on CPU/GPU
- **Accurate**: State-of-the-art person detection
- **Lightweight**: Nano model is only ~6MB

## 📝 License

This project is for educational and demonstration purposes.

## 🆘 Need Help?

1. Check the terminal output for error messages
2. Verify your webcam works in other applications
3. Try different camera indices (0, 1, 2)
4. Ensure no other apps are using the camera

---

**Enjoy real-time person segmentation! 🎉**
