#!/usr/bin/env python3
"""
Setup script for YOLO Person Segmentation Web App
This script will install dependencies and download the YOLO model
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {cmd}")
        print(f"   Error: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ required. Current version: {version.major}.{version.minor}")
        return False
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing dependencies...")
    
    # Check if we're in a virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    if not in_venv:
        print("⚠️  Warning: Not in a virtual environment. Consider creating one:")
        print("   python -m venv venv")
        print("   venv\\Scripts\\activate  # Windows")
        print("   source venv/bin/activate  # Linux/Mac")
        print()
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
        
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing requirements"):
        return False
    
    return True

def download_model():
    """Download YOLO model if not present"""
    print("\n🤖 Checking YOLO model...")
    
    # The model will auto-download when first used
    # We'll use yolo11n-seg.pt which is smaller and faster for demo
    model_path = "yolo11n-seg.pt"
    
    if os.path.exists(model_path):
        print(f"✅ Model already exists: {model_path}")
        return True
    
    print(f"📥 Model will be downloaded automatically on first run")
    print(f"   Using: {model_path} (nano version for faster inference)")
    return True

def main():
    """Main setup function"""
    print("🚀 YOLO Person Segmentation Web App Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Download model
    if not download_model():
        print("\n❌ Setup failed during model download")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ Setup completed successfully!")
    print("\n🎯 Next steps:")
    print("1. Run the application:")
    print("   python app.py")
    print("\n2. Open your browser and go to:")
    print("   http://localhost:8000")
    print("\n3. Make sure your webcam is connected and not being used by other apps")
    print("\n📝 Notes:")
    print("- The YOLO model will download automatically on first run (~6MB)")
    print("- If you have GPU with CUDA, it will be used automatically")
    print("- Press Ctrl+C in the terminal to stop the server")

if __name__ == "__main__":
    main()
