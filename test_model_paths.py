#!/usr/bin/env python3
"""
Test script to check model paths and test switching
"""

import requests
import json

def test_model_paths():
    """Test model path detection and switching"""
    print("🔍 Testing model paths...")
    
    # Get models list
    response = requests.get('http://localhost:8000/models')
    if response.status_code != 200:
        print(f"❌ Failed to get models: {response.status_code}")
        return
    
    data = response.json()
    models = data['models']
    
    print(f"📋 Found {len(models)} models")
    print("\n🎯 First 5 model paths:")
    for i, model in enumerate(models[:5]):
        print(f"   {i+1}. Name: {model['name']}")
        print(f"      Path: '{model['path']}'")
        print(f"      Type: {model['type']}")
        print()
    
    # Test switching to the first downloaded model
    downloaded_models = [m for m in models if m.get('recently_downloaded', False)]
    if downloaded_models:
        test_model = downloaded_models[0]
        print(f"🧪 Testing switch to: {test_model['name']}")
        print(f"   Path: '{test_model['path']}'")
        
        # Try switching
        switch_response = requests.post(
            'http://localhost:8000/switch_model',
            headers={'Content-Type': 'application/json'},
            json={'model_path': test_model['path']}
        )
        
        if switch_response.status_code == 200:
            result = switch_response.json()
            if result['success']:
                print(f"✅ Switch successful: {result['message']}")
            else:
                print(f"❌ Switch failed: {result.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error: {switch_response.status_code}")
    else:
        print("⚠️  No recently downloaded models found to test")

if __name__ == "__main__":
    test_model_paths()
